using UnityEngine;

namespace Modules.UI
{
    public abstract class HorizontalScrollView<TScrollWidget, TScrollWidgetData> : ScrollView<TScrollWidget, TScrollWidgetData> where TScrollWidget : ScrollWidget<TScrollWidgetData>
    {
        [Space]
        [SerializeField]
        [Range(1, 10)]
        private int rowsCount = 1;

        public override void MoveSnapForward()
        {
            MoveToSnapPosition(true);
        }

        public override void MoveSnapBackward()
        {
            MoveToSnapPosition(false);
        }

        protected override void UpdateContentSize()
        {
            var contentWidth = 0f;

            if (HasData)
            {
                var columnCount = Data.Count % rowsCount == 0 ? Data.Count / rowsCount : Data.Count / rowsCount + 1;
                contentWidth = widgetPrefab.Width * columnCount + spacing * (columnCount - 1) + padding.Left + padding.Right;
            }

            horizontalNormalizedPosition = 0;
            content.sizeDelta = new Vector2(contentWidth, content.sizeDelta.y);
        }

        protected override void UpdatePositions()
        {
            var widgetWidth = widgetPrefab.Width;
            var widgetHeight = widgetPrefab.Height;
            var offsetX = widgetWidth + spacing;
            var offsetY = widgetHeight + spacing;
            var initialPosY = rowsCount == 1 ? 0 : -(0.5f * widgetHeight + padding.Top);
            var posX = 0.5f * widgetWidth + padding.Left;
            var count = HasData ? Data.Count : 0;

            positions.Clear();

            for (var i = 0; i < count;)
            {
                var posY = initialPosY;

                for (var r = 0; r < rowsCount; r++)
                {
                    positions.Add(new Vector2(posX, posY));

                    if (++i >= count)
                    {
                        break;
                    }

                    posY -= offsetY;
                }

                posX += offsetX;
            }
        }

        protected override int GetStartIndex()
        {
            var anchorPosX = -content.anchoredPosition.x;
            return Mathf.Max(0, positions.FindIndex(p => p.x > anchorPosX) - rowsCount * IndexOffset);
        }

        protected override int GetNextWidgetCount()
        {
            if (!HasData)
            {
                return 0;
            }

            var widgetWidth = widgetPrefab.Width;
            var viewportWidth = viewport.rect.width;

            return Mathf.Min(Data.Count, Mathf.CeilToInt((viewportWidth + widgetWidth) / widgetWidth) * rowsCount);
        }

        protected override void HandleScrollInput(Vector2 vector)
        {
            if (!HasData)
            {
                return;
            }

            var delta = vector.x * scrollSensitivity * 0.001f;
            var currentValue = horizontalNormalizedPosition;
            var nextValue = Mathf.Clamp01(currentValue + delta);

            if (Mathf.Approximately(currentValue, nextValue))
            {
                return;
            }

            if (horizontalScrollbar == null)
            {
                horizontalNormalizedPosition = nextValue;
            }
            else
            {
                horizontalScrollbar.value = nextValue;
            }
        }

        private void MoveToSnapPosition(bool isForward)
        {
            var offset = 0.5f * widgetPrefab.Width + spacing;
            var anchorPosX = -content.anchoredPosition.x + offset;
            var startIndex = Mathf.Max(0, positions.FindIndex(p => p.x > anchorPosX) - IndexOffset);
            var nextIndex = Mathf.Clamp(startIndex + (isForward ? 1 : -1) * snapScrollStepAmount, 0, positions.Count - 1);
            content.anchoredPosition = new Vector2(-positions[nextIndex].x + offset, content.anchoredPosition.y);
        }
    }
}