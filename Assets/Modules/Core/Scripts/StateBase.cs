using System;
using System.Threading;
using Cysharp.Threading.Tasks;

namespace Modules.Core
{
    public abstract class StateBase : IDisposable
    {
        private readonly CancellationTokenSource disposeCancellationTokenSource = new();

        protected CancellationToken DisposeCancellationToken => disposeCancellationTokenSource.Token;

        public abstract UniTask EnterAsync(CancellationToken cancellationToken = default);

        public virtual UniTask ExitAsync(CancellationToken cancellationToken = default)
        {
            return UniTask.CompletedTask;
        }

        public virtual void Dispose()
        {
            disposeCancellationTokenSource.Cancel();
            disposeCancellationTokenSource.Dispose();
        }
    }
}