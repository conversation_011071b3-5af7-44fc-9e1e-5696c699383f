using System.Collections.Generic;
using UnityEngine;

namespace Modules.Core
{
    public static class TransformExtensions
    {
        public static void SetPose(this Transform transform, Pose pose)
        {
            transform.SetPositionAndRotation(pose.position, pose.rotation);
        }

        public static void SetLocalPose(this Transform transform, Pose pose)
        {
            transform.SetLocalPositionAndRotation(pose.position, pose.rotation);
        }

        public static Pose GetPose(this Transform transform)
        {
            return new Pose(transform.position, transform.rotation);
        }

        public static Pose GetLocalPose(this Transform transform)
        {
            return new Pose(transform.localPosition, transform.localRotation);
        }

        public static Quaternion Reflect(this Quaternion source, Vector3 normal)
        {
            return Quaternion.LookRotation(Vector3.Reflect(source * Vector3.forward, normal), Vector3.Reflect(source * Vector3.up, normal));
        }

        public static List<Pose> GetChildPoses(this Transform transform)
        {
            var result = new List<Pose>();
            foreach (Transform child in transform)
            {
                result.Add(child.GetPose());
            }

            return result;
        }
    }
}