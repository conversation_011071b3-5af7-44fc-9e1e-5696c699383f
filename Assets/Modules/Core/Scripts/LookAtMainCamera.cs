using UnityEngine;

namespace Modules.Core
{
    public class LookAtMainCamera : MonoBehaviour
    {
        [SerializeField] private bool onlyXZ;

        private void Update()
        {
            if (Camera.main == null)
            {
                return;
            }

            var direction = transform.position - Camera.main.transform.position;

            if (onlyXZ)
            {
                direction = direction.OnlyXZ();
            }

            if (direction.IsZero())
            {
                return;
            }

            transform.rotation = Quaternion.LookRotation(direction);
        }
    }
}