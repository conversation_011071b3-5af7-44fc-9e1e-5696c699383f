namespace Modules.CloudRequest
{
    public readonly struct RequestConfig
    {
        public readonly WrapperType wrapperType;
        public readonly SerializerType serializerType;
        public readonly bool useResponseV2;
        public readonly bool useFirebaseToken;

        public RequestConfig(WrapperType wrapperType, SerializerType serializerType, bool useResponseV2, bool useFirebaseToken = false)
        {
            this.wrapperType = wrapperType;
            this.serializerType = serializerType;
            this.useResponseV2 = useResponseV2;
            this.useFirebaseToken = useFirebaseToken;
        }

        public static RequestConfig Old { get; } = new(WrapperType.Body, SerializerType.Default, false);
        public static RequestConfig New { get; } = new(WrapperType.Data, SerializerType.Default, false);
        public static RequestConfig Firebase { get; } = new(WrapperType.Data, SerializerType.Default, false, true);
    }
}