using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Modules.Core.Audio
{
    internal class AudioClient : MonoBehaviour, IAudioClient
    {
        [SerializeField] private AudioPlayer audioPlayerPrefab;
        [SerializeField] private List<AudioCollection> audioCollectionList;

        private AudioPlayerPool audioPlayerPool;

        private IReadOnlyList<AudioPlayer> AudioPlayerList => audioPlayerPool.ActivePoolItems;

        private void Awake()
        {
            audioPlayerPool = new AudioPlayerPool(audioPlayerPrefab, transform);
        }

        public void Play(string key, Vector3 position = default, CancellationToken cancellationToken = default)
        {
            PlayAsync(key, position, false, cancellationToken).Forget();
        }

        public void Play(string key, Transform parent, CancellationToken cancellationToken = default)
        {
            PlayAsync(key, parent, false, cancellationToken).Forget();
        }

        public void PlayOneInstance(string key, Vector3 position = default, CancellationToken cancellationToken = default)
        {
            PlayAsync(key, position, true, cancellationToken).Forget();
        }

        public void PlayOneInstance(string key, Transform parent, CancellationToken cancellationToken = default)
        {
            PlayAsync(key, parent, true, cancellationToken).Forget();
        }

        public async UniTask PlayAsync(string key, Vector3 position = default, bool isOneInstance = false, CancellationToken cancellationToken = default)
        {
            if ((isOneInstance && IsPlaying(key)) || !TryGetAudioData(key, out var audioData))
            {
                return;
            }

            var audioPlayer = CreateAudioPlayer(key, audioData);
            audioPlayer.SetPosition(position);
            await audioPlayer.PlayAsync(cancellationToken);
        }

        public async UniTask PlayAsync(string key, Transform parent, bool isOneInstance = false, CancellationToken cancellationToken = default)
        {
            if ((isOneInstance && IsPlaying(key)) || !TryGetAudioData(key, out var audioData))
            {
                return;
            }

            var audioPlayer = CreateAudioPlayer(key, audioData);
            audioPlayer.SetParent(parent);
            audioPlayer.SetLocalPosition(Vector3.zero);
            await audioPlayer.PlayAsync(cancellationToken);
        }

        public void Stop(string key)
        {
            for (var i = AudioPlayerList.Count - 1; i >= 0; i--)
            {
                var audioPlayer = AudioPlayerList[i];

                if (audioPlayer.Key == key)
                {
                    audioPlayer.Stop();
                }
            }
        }

        public void StopAll()
        {
            for (var i = AudioPlayerList.Count - 1; i >= 0; i--)
            {
                AudioPlayerList[i].Stop();
            }
        }

        public bool IsPlaying(string key)
        {
            foreach (var audioPlayer in AudioPlayerList)
            {
                if (audioPlayer.Key == key)
                {
                    return true;
                }
            }

            return false;
        }

        public AudioClip GetClip(string key)
        {
            return GetAudioData(key)?.clip;
        }

        public void AddAudioCollection(AudioCollection audioCollection)
        {
            if (audioCollection == null || audioCollectionList.Exists(x => x == audioCollection))
            {
                return;
            }

            audioCollectionList.Add(audioCollection);
        }

        public void RemoveAudioCollection(AudioCollection audioCollection)
        {
            if (audioCollection == null || !audioCollectionList.Exists(x => x == audioCollection))
            {
                return;
            }

            audioCollectionList.Remove(audioCollection);
        }

        private AudioData GetAudioData(string key)
        {
            for (var i = 0; i < audioCollectionList.Count; i++)
            {
                var audioCollection = audioCollectionList[i];

                if (audioCollection == null)
                {
                    continue;
                }

                var audioData = audioCollection.GetAudioData(key);

                if (audioData != null)
                {
                    return audioData;
                }
            }

            return null;
        }

        private bool TryGetAudioData(string key, out AudioData audioData)
        {
            audioData = GetAudioData(key);
            var hasData = audioData != null;

            if (!hasData)
            {
                Log.Audio.Warn("Audio data is empty. Key: {0}", key);
            }

            return hasData;
        }

        private AudioPlayer CreateAudioPlayer(string key, AudioData audioData)
        {
            var audioPlayer = audioPlayerPool.Get();
            audioPlayer.Initialize(key, audioData, audioPlayerPool);

            return audioPlayer;
        }
    }
}