using System;
using System.Reactive.Subjects;

namespace Modules.Network
{
    internal static class NetworkActorDispatcher
    {
        private static readonly ISubject<(InjectionType, NetworkActor)> onInjected = new Subject<(InjectionType, NetworkActor)>();
        private static readonly ISubject<NetworkActor> onSpawned = new Subject<NetworkActor>();
        private static readonly ISubject<NetworkActor> onDespawned = new Subject<NetworkActor>();

        public static IObservable<(InjectionType injectionType, NetworkActor networkActor)> OnInjected => onInjected;
        public static IObservable<NetworkActor> OnSpawned => onSpawned;
        public static IObservable<NetworkActor> OnDespawned => onDespawned;

        public static void Inject(InjectionType injectionType, NetworkActor networkActor)
        {
            onInjected.OnNext((injectionType, networkActor));
        }

        public static void Spawned(NetworkActor networkActor)
        {
            onSpawned.OnNext(networkActor);
        }

        public static void Despawned(NetworkActor networkActor)
        {
            onDespawned.OnNext(networkActor);
        }
    }
}