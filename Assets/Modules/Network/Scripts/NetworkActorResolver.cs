using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using VContainer;
using VContainer.Unity;

namespace Modules.Network
{
    internal class NetworkActorResolver : IStartable, IDisposable
    {
        private readonly IObjectResolver objectResolver;
        private readonly CancellationTokenSource disposeCancellationTokenSource = new();

        public NetworkActorResolver(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        void IStartable.Start()
        {
            NetworkActorDispatcher.OnInjected.Subscribe(args =>
            {
                if (args.injectionType == InjectionType.GameObject)
                {
                    objectResolver.InjectGameObject(args.networkActor.gameObject);
                }
                else if (args.injectionType == InjectionType.Component)
                {
                    objectResolver.Inject(args.networkActor);
                }
            }).AddTo(disposeCancellationTokenSource.Token);
        }

        void IDisposable.Dispose()
        {
            disposeCancellationTokenSource.CancelAndDispose();
        }
    }
}