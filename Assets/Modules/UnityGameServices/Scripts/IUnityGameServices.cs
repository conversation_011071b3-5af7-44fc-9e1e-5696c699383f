using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;

namespace Modules.UnityGameServices
{
    public interface IUnityGameServices : IDisposable
    {
        UniTask<Result> Initialize(string apiName, CancellationToken cancellationToken);

        // Authentication
        string UserId { get; }
        UniTask<Result> SignInAnonymously(CancellationToken cancellationToken);
        UniTask<Result> SignInWithOculus(string nonce, string userId, CancellationToken cancellationToken);
        UniTask<Result> SignInWithUsernamePassword(string username, string password, CancellationToken cancellationToken);
        UniTask<Result> SignUpWithUsernamePassword(string username, string password, CancellationToken cancellationToken);
        UniTask<Result<string>> GetUserName(CancellationToken cancellationToken);
        UniTask<Result> SetUserName(string userName, CancellationToken cancellationToken);

        // User Data
        UniTask<Result<CloudData>> GetUserData(HashSet<string> keys, CancellationToken cancellationToken);
        UniTask<Result> SetUserData(Dictionary<string, object> data, CancellationToken cancellationToken);

        // Economy
        UniTask<Result> SyncEconomyConfiguration(CancellationToken cancellationToken);
        UniTask<Result<int>> GetBalance(string currencyId, CancellationToken cancellationToken);
        UniTask<Result> SetBalance(string currencyId, int balance, CancellationToken cancellationToken);
        UniTask<Result<int>> IncrementBalance(string currencyId, int amount, CancellationToken cancellationToken);
        UniTask<Result<List<RewardDefinition>>> Purchase(string purchaseId, CancellationToken cancellationToken);
        Result<List<InventoryDefinition<TCustomData>>> GetInventoryDefinitionList<TCustomData>();
        public Result<List<PurchaseDefinition<TCustomData>>> GetPurchaseDefinitionList<TCustomData>();
        UniTask<Result<UserInventoryDefinition>> GetUserInventory(CancellationToken cancellationToken);
        UniTask<Result> AddUserInventory(List<string> inventoryIdList, CancellationToken cancellationToken);
        UniTask<Result> AddUserInventory(string inventoryId, CancellationToken cancellationToken);

        // Remote Config
        UniTask<Result> GetRemoteConfig(CancellationToken cancellationToken);
        bool TryGetJsonConfig(string key, out string result);
        bool TryGetStringConfig(string key, out string result);
        bool TryGetBoolConfig(string key, out bool result);
        bool TryGetFloatConfig(string key, out float result);
        bool TryGetIntConfig(string key, out int result);

        // Leaderboard
        UniTask<Result<List<LeaderboardScore>>> GetLeaderboardScoreList(string leaderboardId, int limit, CancellationToken cancellationToken);
        UniTask<Result<LeaderboardScore>> GetUserLeaderboardScore(string leaderboardId, CancellationToken cancellationToken);
        UniTask<Result> AddUserLeaderboardScore(string leaderboardId, int score, CancellationToken cancellationToken);

        // Cloud Code
        UniTask<Result<T>> SendCloudCodeRequest<T>(string functionName, Dictionary<string, object> arguments, CancellationToken cancellationToken) where T : CloudCodeResponse;
    }
}