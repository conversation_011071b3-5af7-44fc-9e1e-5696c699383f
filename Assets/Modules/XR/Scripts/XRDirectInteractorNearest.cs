using System.Collections.Generic;

namespace Modules.XR
{
    public class XRDirectInteractorNearest : UnityEngine.XR.Interaction.Toolkit.Interactors.XRDirectInteractor
    {
        public override void GetValidTargets(List<UnityEngine.XR.Interaction.Toolkit.Interactables.IXRInteractable> targets)
        {
            base.GetValidTargets(targets);

            if (targets.Count > 1)
            {
                var target = targets[0];
                targets.Clear();
                targets.Add(target);
            }
        }
    }
}