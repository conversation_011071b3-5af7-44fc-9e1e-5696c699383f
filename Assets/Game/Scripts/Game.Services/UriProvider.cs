using System;
using System.Collections.Generic;
using Modules.CloudRequest;
using Modules.Core;

namespace Game.Services
{
    internal class UriProvider : UriProviderBase
    {
        public UriProvider(AppConfig appConfig) : base(appConfig.FirebaseBaseUri)
        {
        }

        public Uri GetFirebaseToken() => CreateFirebaseUri("gettoken");
        public Uri CreateLevel() => CreateFirebaseUri("createlevel");
        public Uri GetLevel(IDictionary<string, string> query) => CreateFirebaseUri("getleveldata", query);
        public Uri UpdateLevel(IDictionary<string, string> query) => CreateFirebaseUri("saveleveldata", query);
        public Uri GetLevelList() => CreateFirebaseUri("getLevelList");
        public Uri GetPromoLevelList() => CreateFirebaseUri("getPromoLevelList");
        public Uri GetLobbyLevelList() => CreateFirebaseUri("getlobbylevellist");
        public Uri GetMapUploadUrl(IDictionary<string, string> query) => CreateFirebaseUri("getmapuploadurl", query);
        public Uri GetMapDownloadUrl(IDictionary<string, string> query) => CreateFirebaseUri("getmapdownloadurl", query);
    }
}