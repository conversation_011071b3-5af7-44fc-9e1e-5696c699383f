using System;
using UnityEngine;

namespace Game.Core.Data
{
    [Serializable]
    public class MonsterData
    {
        public byte code;
        public Vector3 spawn;
        public float rotation;
        public Vector3 patrolCenter;
        public Vector3 patrolArea;

        public MonsterData(byte code, Vector3 spawn, float rotation, Vector3 patrolCenter, Vector3 patrolArea)
        {
            this.code = code;
            this.spawn = spawn;
            this.rotation = rotation;
            this.patrolCenter = patrolCenter;
            this.patrolArea = patrolArea;
        }

        public bool HasPatrolArea => patrolArea.magnitude > 0f;
    }
}