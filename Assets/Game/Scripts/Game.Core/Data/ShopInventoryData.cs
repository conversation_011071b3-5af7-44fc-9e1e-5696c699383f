using System;
using Game.Core.Extensions;

namespace Game.Core.Data
{
    [Serializable]
    public class ShopInventoryData
    {
        public string id;
        public string viewCode;
        public string title;
        public int level;
        public InventoryCategory category;
        public bool isPurchased;

        public InventoryRarity Rarity => ShopUtility.GetRarity(level);
        public bool IsUnavailable { get; set; }
        public bool IsOwned => !IsUnavailable && isPurchased;
        public bool IsOwnedInteractable => IsOwned && IsInteractable;
        public bool IsInteractable => category is InventoryCategory.Weapon or InventoryCategory.Gun or InventoryCategory.Wing or InventoryCategory.Broom or InventoryCategory.BlockTool;
        public bool IsAvatar => category is InventoryCategory.Avatar;
        public bool IsHat => category is InventoryCategory.Hat;
        public bool IsSuit => category is InventoryCategory.Suit;
        public bool IsOutfit => IsHat || IsSuit;
        public bool IsAvatarOrOutfit => IsAvatar || IsOutfit;
        public bool IsBadge => category is InventoryCategory.Badge;

        public override string ToString()
        {
            return title;
        }
    }
}