using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Atm;
using Game.Views.UI.Screens.MiniAtm;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Shop
{
    public class AtmViewController : ControllerBase
    {
        private AtmManager atmManager;
        private EconomyModel economyModel;
        private MiniAtmScreen miniAtmScreen;

        [Inject]
        private void Construct(GameModel gameModel, EconomyModel economyModel, AtmManager atmManager, IScreenManager screenManager)
        {
            this.atmManager = atmManager;
            this.economyModel = economyModel;
            miniAtmScreen = screenManager.GetScreen<MiniAtmScreen>(true);

            gameModel.IsInitialized.Where(ok => ok).Subscribe(_ => HandleInitialize()).AddTo(DisposeCancellationToken);
        }

        private void HandleInitialize()
        {
            var coinShopItemList = economyModel.ShopItemList.FindAll(i => i.IsCoinItem);
            coinShopItemList.Sort((a, b) => a.coinAmount.CompareTo(b.coinAmount));
            atmManager.Initialize(coinShopItemList);
            miniAtmScreen.Initialize(coinShopItemList);

            atmManager.OnPurchaseClicked.Subscribe(args => HandlePurchase(args, AnalyticsSource.Atm)).AddTo(DisposeCancellationToken);
            miniAtmScreen.OnPurchaseClicked.Subscribe(args => HandlePurchase(args, AnalyticsSource.MiniAtm)).AddTo(DisposeCancellationToken);
            economyModel.CoinAmount.Subscribe(HandleCoinAmount).AddTo(DisposeCancellationToken);
        }

        private void HandlePurchase(PurchaseClickArgs args, string source)
        {
            economyModel.Purchase(args.shopItem, source, DisposeCancellationToken).Forget();
        }

        private void HandleCoinAmount(int coinAmount)
        {
            atmManager.SetCoinAmount(coinAmount);
        }
    }
}