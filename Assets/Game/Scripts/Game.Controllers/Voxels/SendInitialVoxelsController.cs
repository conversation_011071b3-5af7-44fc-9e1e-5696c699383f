using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Models;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class SendInitialVoxelsController : ControllerBase
    {
        private const int CaptureCountInFrame = 10;

        private VoxelModel voxelModel;
        private PlayersModel playersModel;
        private VoxelSpaceManager voxelSpaceManager;

        private List<SliceVoxel> SliceVoxels => voxelModel.SliceVoxels;
        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private int SliceServerTime => voxelSpaceManager.SliceServerTime.Value;

        [Inject]
        private void Construct(VoxelModel voxelModel, VoxelSpaceManager voxelSpaceManager, PlayersModel playersModel)
        {
            this.voxelModel = voxelModel;
            this.voxelSpaceManager = voxelSpaceManager;
            this.playersModel = playersModel;

            voxelSpaceManager.OnInitialVoxelsSending.Subscribe(HandleInitialVoxelsSending).AddTo(DisposeCancellationToken);
        }

        private void HandleInitialVoxelsSending(PlayerRef receiver)
        {
            if (LocalPlayer != null && LocalPlayer.IsInitialVoxelsReceived.Value && playersModel.TryGetPlayer(receiver, out var player))
            {
                SendInitialVoxels(player).Forget();
            }
        }

        private async UniTaskVoid SendInitialVoxels(PlayerActor receiver)
        {
            if (SliceVoxels.Count == 0)
            {
                GameLogger.Voxels.Debug("Initial voxels are empty. Nothing to send to player: {0}.", receiver.PlayerId);
                voxelSpaceManager.CompleteInitialVoxels(receiver.StateAuthority);
                return;
            }

            var captureCounter = 0;
            var sliceVoxels = GetSliceVoxels(receiver.transform.position);
            var token = CancellationTokenSource.CreateLinkedTokenSource(LocalPlayer.destroyCancellationToken, receiver.destroyCancellationToken).Token;

            foreach (var capture in sliceVoxels)
            {
                if (capture.isDestroyed)
                {
                    voxelSpaceManager.DestroyInitialVoxelNetworked(receiver.StateAuthority, capture.position, capture.time);
                }
                else
                {
                    voxelSpaceManager.CreateInitialVoxelNetworked(receiver.StateAuthority, capture.position, capture.id, capture.time);
                }

                captureCounter++;
                if (captureCounter > CaptureCountInFrame)
                {
                    captureCounter = 0;
                    await UniTask.Yield(token);
                }
            }

            voxelSpaceManager.CompleteInitialVoxels(receiver.StateAuthority);
            GameLogger.Voxels.Debug("Initial voxels were sent to player {0}. Initial voxel count: {1}", receiver.PlayerId, sliceVoxels.Count);
        }

        private List<SliceVoxel> GetSliceVoxels(Vector3 origin)
        {
            var time = Mathf.Max(0, SliceServerTime - 5);
            var sliceVoxels = new List<SliceVoxel>(SliceVoxels.Count);

            for (var i = 0; i < SliceVoxels.Count; i++)
            {
                var voxel = SliceVoxels[i];
                if (voxel.time < time)
                {
                    continue;
                }

                sliceVoxels.Add(voxel);
            }

            sliceVoxels.Sort((x, y) => GetSqrDistance(x.position, origin).CompareTo(GetSqrDistance(y.position, origin)));
            return sliceVoxels;
        }

        private static float GetSqrDistance(Vector3 start, Vector3 end)
        {
            return (start - end).sqrMagnitude;
        }
    }
}