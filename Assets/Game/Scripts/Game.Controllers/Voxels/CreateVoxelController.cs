using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.BuildIslands;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.UI.Screens.Notification;
using Game.Views.Voxels;
using Game.Views.World;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using VoxelPlay;

namespace Game.Controllers.Voxels
{
    public class CreateVoxelController : ControllerBase
    {
        private bool isLeftHandPressed;
        private bool isRightHandPressed;
        private bool hasBuildToolInLeftHand;
        private bool hasBuildToolInRightHand;
        private IXRInput xrInput;
        private LevelModel levelModel;
        private VoxelModel voxelModel;
        private VoxelConfig voxelConfig;
        private IAudioClient audioClient;
        private WorldBoundary worldBoundary;
        private BlockToolActor leftBlockTool;
        private BlockToolActor rightBlockTool;
        private BuildZoneHandler buildZoneHandler;
        private VoxelSpaceManager voxelSpaceManager;
        private NotificationScreen notificationScreen;
        private BlockInventoryModel blockInventoryModel;
        private CancellationTokenSource createVoxelCancellationTokenSource;
        private PlayersModel playersModel;

        private int SelectedVoxelId => voxelModel.SelectedVoxelId.Value;
        private bool IsCreateVoxels => levelModel.LevelConfig.IsCreateVoxels;
        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        private Vector3 VoxelPoint
        {
            get => voxelSpaceManager.PlaceVoxelPoint;
            set => voxelSpaceManager.PlaceVoxelPoint = value;
        }

        public override void Dispose()
        {
            base.Dispose();
            createVoxelCancellationTokenSource.CancelAndDispose();
        }

        private void HandleIsConnected(bool ok)
        {
            hasBuildToolInLeftHand = false;
            hasBuildToolInRightHand = false;
            createVoxelCancellationTokenSource.CancelAndDispose();

            if (ok && IsCreateVoxels)
            {
                createVoxelCancellationTokenSource = new CancellationTokenSource();
                UniTaskAsyncEnumerable.EveryUpdate(PlayerLoopTiming.LastUpdate).Subscribe(_ => Update()).AddTo(createVoxelCancellationTokenSource.Token);
            }
            else
            {
                voxelSpaceManager.HidePreviewVoxel();
            }
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (args.interactable is not BlockToolActor blockTool)
            {
                return;
            }

            if (blockTool.IsLeftInteractor)
            {
                hasBuildToolInLeftHand = true;
                leftBlockTool = blockTool;
            }
            else if (blockTool.IsRightInteractor)
            {
                hasBuildToolInRightHand = true;
                rightBlockTool = blockTool;
            }
        }

        private void HandleSelectExited(InteractionArgs<SelectExitEventArgs> args)
        {
            if (args.interactable is not BlockToolActor blockTool)
            {
                return;
            }

            if (blockTool.IsLeftInteractor)
            {
                hasBuildToolInLeftHand = false;
                leftBlockTool = null;
            }
            else if (blockTool.IsRightInteractor)
            {
                hasBuildToolInRightHand = false;
                rightBlockTool = null;
            }
        }

        private void Update()
        {
            if (xrInput.WasLeftActivateReleasedThisFrame)
            {
                if (isLeftHandPressed)
                {
                    TryCreateVoxel();
                }

                isLeftHandPressed = false;
            }

            if (xrInput.WasRightActivateReleasedThisFrame)
            {
                if (isRightHandPressed)
                {
                    TryCreateVoxel();
                }

                isRightHandPressed = false;
            }

            if (hasBuildToolInLeftHand && xrInput.WasLeftActivatePerformedThisFrame)
            {
                isLeftHandPressed = !isRightHandPressed;
            }
            else if (hasBuildToolInLeftHand && isLeftHandPressed)
            {
                RenderPreviewVoxel(leftBlockTool.BarrelNode);
            }
            else if (hasBuildToolInRightHand && xrInput.WasRightActivatePerformedThisFrame)
            {
                isRightHandPressed = !isLeftHandPressed;
            }
            else if (hasBuildToolInRightHand && isRightHandPressed)
            {
                RenderPreviewVoxel(rightBlockTool.BarrelNode);
            }
            else
            {
                isLeftHandPressed = false;
                isRightHandPressed = false;
                voxelSpaceManager.HidePreviewVoxel();
            }
        }

        private bool Raycast(Transform node, out VoxelHitInfo hit)
        {
            return Raycast(node.position, node.forward, out hit);
        }

        [Inject]
        private void Construct(
            IXRInput xrInput,
            LevelModel levelModel,
            VoxelModel voxelModel,
            VoxelConfig voxelConfig,
            IAudioClient audioClient,
            WorldBoundary worldBoundary,
            IScreenManager screenManager,
            INetworkClient networkClient,
            BuildZoneHandler buildZoneHandler,
            VoxelSpaceManager voxelSpaceManager,
            BlockInventoryModel blockInventoryModel,
            IInteractableInteractionSubscriber interactableInteractionsSubscriber,
            PlayersModel playersModel)
        {
            this.xrInput = xrInput;
            this.levelModel = levelModel;
            this.voxelModel = voxelModel;
            this.voxelConfig = voxelConfig;
            this.audioClient = audioClient;
            this.worldBoundary = worldBoundary;
            this.buildZoneHandler = buildZoneHandler;
            this.voxelSpaceManager = voxelSpaceManager;
            this.blockInventoryModel = blockInventoryModel;
            this.playersModel = playersModel;
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            networkClient.IsConnected.Subscribe(HandleIsConnected).AddTo(DisposeCancellationToken);
            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(DisposeCancellationToken);
            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleSelectExited).AddTo(DisposeCancellationToken);
        }

        private bool Raycast(Vector3 origin, Vector3 direction, out VoxelHitInfo hit)
        {
            return voxelSpaceManager.RaycastVoxel(origin, direction, out hit);
        }

        private void RenderPreviewVoxel(Transform node)
        {
            Vector3 placeVoxelPoint;

            if (Raycast(node, out var hit))
            {
                placeVoxelPoint = hit.voxelCenter + hit.normal;
            }
            else
            {
                var point = node.position + node.forward * voxelConfig.RaycastDistance;
                var voxelCenter = voxelSpaceManager.GetVoxelCenter(point);
                placeVoxelPoint = voxelSpaceManager.OverlapVoxel(voxelCenter) ? voxelCenter : Vector3.zero;
            }

            if (placeVoxelPoint == Vector3.zero)
            {
                VoxelPoint = Vector3.zero;
                voxelSpaceManager.HidePreviewVoxel();
            }
            else
            {
                VoxelPoint = placeVoxelPoint;
                voxelSpaceManager.RenderPreviewVoxel(node.position, VoxelPoint);
            }
        }

        private void TryCreateVoxel()
        {
            if (!IsPointAvailable(VoxelPoint))
            {
                return;
            }

            if (buildZoneHandler.IsValidPoint(VoxelPoint))
            {
                if (levelModel.Level.useBlockInventory
                    && !voxelConfig.IsInfiniteVoxelDef(SelectedVoxelId)
                    && !blockInventoryModel.TryGiveAwayBlock(SelectedVoxelId))
                {
                    audioClient.Play(AudioKeys.ProhibitedBuildZone, VoxelPoint, DisposeCancellationToken);
                    return;
                }

                var rotation = GetLocalPlayerLookRotation();
                voxelSpaceManager.CreateVoxelNetworked(VoxelPoint, SelectedVoxelId, false, rotation);
            }
            else
            {
                notificationScreen.Show("You can build only inside a build zone", 3);
            }
        }

        private byte GetLocalPlayerLookRotation()
        {
            if (LocalPlayer == null)
            {
                return 0;
            }

            var playerPosition = LocalPlayer.transform.position;
            var directionToPlayer = playerPosition - VoxelPoint;

            directionToPlayer.y = 0;

            if (directionToPlayer.sqrMagnitude < 0.001f)
            {
                return 0;
            }

            directionToPlayer.Normalize();

            var angle = Mathf.Atan2(directionToPlayer.x, directionToPlayer.z) * Mathf.Rad2Deg;
            if (angle < 0) angle += 360f;

            if (angle >= 315f || angle < 45f)
            {
                return 0;
            }

            if (angle >= 45f && angle < 135f)
            {
                return 1;
            }

            if (angle >= 135f && angle < 225f)
            {
                return 2;
            }

            return 3;
        }

        private bool IsPointAvailable(Vector3 point)
        {
            return point != Vector3.zero
                   && voxelSpaceManager.IsMapLoaded.Value
                   && IsPointInWorldBoundary(point)
                   && !voxelSpaceManager.CheckCollision(point)
                   && !voxelSpaceManager.OverlapPlayer(point);
        }

        private bool IsPointInWorldBoundary(Vector3 point)
        {
            var worldTemplate = voxelConfig.GetWorldTemplateOrDefault(levelModel.Level.worldTemplateId);
            if (worldTemplate.DisableBounds)
            {
                return point.x > -worldTemplate.LevelBoundsHorizontalLimitSoft
                       || point.x < worldTemplate.LevelBoundsHorizontalLimitSoft
                       || point.z > -worldTemplate.LevelBoundsHorizontalLimitSoft
                       || point.z < worldTemplate.LevelBoundsHorizontalLimitSoft
                       || point.y > -worldTemplate.LevelBoundsVerticalLimitSoft
                       || point.y < worldTemplate.LevelBoundsVerticalLimitSoft;
            }

            return worldBoundary.Contains(point);
        }
    }
}