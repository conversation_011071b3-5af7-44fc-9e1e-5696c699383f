using System;
using Cysharp.Threading.Tasks;
using Game.View.Locomotions;
using Game.Views.Voxels;
using Modules.Core;
using Modules.XR;
using VContainer;

namespace Game.Controllers.Locomotions
{
    public class ClimbLocomotionController : ControllerBase
    {
        private IXRInput xrInput;
        private IAudioClient audioClient;
        private VoxelConfig voxelConfig;
        private LocomotionSystem locomotionSystem;

        [Inject]
        private void Construct(LocomotionSystem locomotionSystem, IXRInput xrInput, IAudioClient audioClient, VoxelConfig voxelConfig)
        {
            this.locomotionSystem = locomotionSystem;
            this.xrInput = xrInput;
            this.audioClient = audioClient;
            this.voxelConfig = voxelConfig;

            if (locomotionSystem.TryGetActiveLocomotion<ClimbLocomotion>(out var climbLocomotion))
            {
                climbLocomotion.OnHandActionLeft.Subscribe(action => HandleHandAction(action, HandType.Left)).AddTo(DisposeCancellationToken);
                climbLocomotion.OnHandActionRight.Subscribe(action => HandleHandAction(action, HandType.Right)).AddTo(DisposeCancellationToken);
            }
        }

        public override void Initialize()
        {
            SetupLocomotionInteractions();
        }

        private void HandleHandAction(ClimbLocomotion.HandAction handAction, HandType handType)
        {
            switch (handAction.type)
            {
                case ClimbLocomotion.HandActionType.Grab:
                    audioClient.Play(voxelConfig.VoxelClimbAudioKey, handAction.position, DisposeCancellationToken);
                    xrInput.SendHapticImpulse(handType, HapticImpulse.ShortDurationMediumAmplitude);
                    break;
                case ClimbLocomotion.HandActionType.Release:
                    break;
                case ClimbLocomotion.HandActionType.ForceRelease:
                    xrInput.SendHapticImpulse(handType, HapticImpulse.MediumDurationHighAmplitude);
                    break;
            }
        }

        private void SetupLocomotionInteractions()
        {
            var hasClimbLocomotion = locomotionSystem.TryGetLocomotion<ClimbLocomotion>(out var climbLocomotion);
            var hasArmLocomotion = locomotionSystem.TryGetLocomotion<ArmLocomotion>(out var armLocomotion);

            if (hasClimbLocomotion && hasArmLocomotion)
            {
                climbLocomotion.OnClimbStarted.Subscribe(_ => armLocomotion.EnableTouchNotifications(false)).AddTo(DisposeCancellationToken);
                climbLocomotion.OnClimbEnded.Subscribe(_ => armLocomotion.EnableTouchNotifications(true)).AddTo(DisposeCancellationToken);
            }
        }
    }
}