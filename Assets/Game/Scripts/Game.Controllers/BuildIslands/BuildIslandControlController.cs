using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Services;
using Game.Views.BuildIslands;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.BuildIslands
{
    public class BuildIslandControlController : ControllerBase
    {
        private VoxelModel voxelModel;
        private INetworkClient networkClient;
        private VoxelSpaceManager voxelSpaceManager;
        private IBuildIslandService buildIslandService;
        private BuildIslandsManager buildIslandsManager;

        private bool IsMasterClient => networkClient.IsMasterClient.Value;

        [Inject]
        private void Construct(
            VoxelModel voxelModel,
            PlayersModel playersModel,
            INetworkClient networkClient,
            VoxelSpaceManager voxelSpaceManager,
            IBuildIslandService buildIslandService,
            BuildIslandsManager buildIslandsManager)
        {
            this.voxelModel = voxelModel;
            this.networkClient = networkClient;
            this.voxelSpaceManager = voxelSpaceManager;
            this.buildIslandService = buildIslandService;
            this.buildIslandsManager = buildIslandsManager;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            buildIslandsManager.OnClearClicked.Subscribe(HandleClearClicked).AddTo(player);
            buildIslandsManager.OnResetClicked.Subscribe(HandleResetClicked).AddTo(player);
            buildIslandsManager.OnLoadClicked.Subscribe(b => HandleLoadClicked(b).Forget()).AddTo(player);
            buildIslandsManager.OnSaveClicked.Subscribe(HandleSaveClicked).AddTo(player);
        }

        private void HandleClearClicked(BuildIslandActor buildIsland)
        {
            voxelSpaceManager.DestroyVoxelsInBounds(buildIsland.Bounds, HandleVoxelDestroyed);

            if (IsMasterClient)
            {
                voxelModel.SaveSliceVoxels();
            }
        }

        private void HandleResetClicked(BuildIslandActor buildIsland)
        {
            voxelSpaceManager.DestroyVoxelsInBounds(buildIsland.Bounds, HandleVoxelDestroyed);
            voxelSpaceManager.CreateModel(buildIslandsManager.DefaultIslandModel, buildIsland.OriginPose.position, (int)buildIsland.OriginPose.rotation.eulerAngles.y);

            if (IsMasterClient)
            {
                voxelModel.SaveSliceVoxels();
            }
        }

        private async UniTaskVoid HandleLoadClicked(BuildIslandActor buildIsland)
        {
            var map = buildIslandService.GetBuildIslandMap(1);
            if (map == null)
            {
                return;
            }

            buildIsland.Clear();
            await UniTask.Delay(500, cancellationToken: buildIsland.DespawnCancellationToken);
            voxelSpaceManager.SetBuildIslandMap(map, buildIsland.OriginPose);
        }

        private void HandleSaveClicked(BuildIslandActor buildIsland)
        {
            var map = voxelSpaceManager.GetBuildIslandMap(buildIsland.Bounds, buildIsland.OriginPose);
            buildIslandService.SaveBuildIslandMap(1, map);
        }

        private void HandleVoxelDestroyed(Vector3 point)
        {
            voxelModel.SliceVoxels.RemoveAll(v => v.position == point);
        }
    }
}