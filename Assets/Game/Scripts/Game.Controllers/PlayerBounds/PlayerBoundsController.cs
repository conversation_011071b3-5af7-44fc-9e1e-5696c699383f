using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Checkpoints;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.UI.Screens.Notification;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using Modules.XR;
using VContainer;

namespace Game.Controllers.PlayerBounds
{
    public class PlayerBoundsController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private CheckpointsManager checkpointsManager;
        private VoxelConfig voxelConfig;
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private NotificationScreen notificationScreen;

        private float softBoundsTime;

        private const float BoundsCheckInterval = 0.5f;

        [Inject]
        private void Construct(
            IXRPlayer xrPlayer,
            PlayersModel playersModel,
            CheckpointsManager checkpointsManager,
            VoxelConfig voxelConfig,
            LevelModel levelModel,
            IScreenManager screenManager,
            INetworkClient networkClient)
        {
            this.xrPlayer = xrPlayer;
            this.voxelConfig = voxelConfig;
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.checkpointsManager = checkpointsManager;
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            UniTaskAsyncEnumerable
                .Interval(TimeSpan.FromSeconds(BoundsCheckInterval))
                .Where(_ => networkClient.IsConnected.Value)
                .Subscribe(_ => CheckPlayerBounds())
                .AddTo(DisposeCancellationToken);
        }

        private void CheckPlayerBounds()
        {
            if (IsPlayerOutOfBoundsHard())
            {
                TeleportPlayer();
            }
            else if (IsPlayerOutOfBoundsSoft())
            {
                if (softBoundsTime == 0f)
                {
                    notificationScreen.Show("You are out of bounds.\nTeleporting back in 5 seconds", 4);
                }

                softBoundsTime += BoundsCheckInterval;
                if (softBoundsTime >= 5f)
                {
                    TeleportPlayer();
                }
            }
            else
            {
                if (softBoundsTime > 0)
                {
                    notificationScreen.Show("You are back in bounds", 2);
                }

                softBoundsTime = 0f;
            }
        }

        private void TeleportPlayer()
        {
            softBoundsTime = 0f;
            if (checkpointsManager.IsAnyRaceActive)
            {
                playersModel.TeleportLocalPlayer(new PlayerTeleportArgs(checkpointsManager.SpawnPoint.GetPose()));
            }
            else
            {
                playersModel.RespawnLocalPlayer(new PlayerRespawnArgs(true));
            }
        }

        private bool IsPlayerOutOfBoundsHard()
        {
            var playerPosition = xrPlayer.HeadNode.position;
            if (playerPosition.y < -GetLevelVerticalBoundsHard() ||
                playerPosition.y > GetLevelVerticalBoundsHard() ||
                playerPosition.x < -GetLevelHorizontalBoundsHard() ||
                playerPosition.x > GetLevelHorizontalBoundsHard() ||
                playerPosition.z < -GetLevelHorizontalBoundsHard() ||
                playerPosition.z > GetLevelHorizontalBoundsHard())
            {
                return true;
            }

            return false;
        }

        private bool IsPlayerOutOfBoundsSoft()
        {
            var playerPosition = xrPlayer.HeadNode.position;
            if (playerPosition.y < -GetLevelVerticalBoundsSoft() ||
                playerPosition.y > GetLevelVerticalBoundsSoft() ||
                playerPosition.x < -GetLevelHorizontalBoundsSoft() ||
                playerPosition.x > GetLevelHorizontalBoundsSoft() ||
                playerPosition.z < -GetLevelHorizontalBoundsSoft() ||
                playerPosition.z > GetLevelHorizontalBoundsSoft())
            {
                return true;
            }

            return false;
        }

        private int GetLevelVerticalBoundsSoft()
        {
            if (levelModel.Level == null)
            {
                return 1000;
            }

            var worldTemplate = voxelConfig.GetWorldTemplateOrDefault(levelModel.Level.worldTemplateId);

            return worldTemplate.LevelBoundsVerticalLimitSoft;
        }

        private int GetLevelVerticalBoundsHard()
        {
            if (levelModel.Level == null)
            {
                return 1000;
            }

            var worldTemplate = voxelConfig.GetWorldTemplateOrDefault(levelModel.Level.worldTemplateId);

            return worldTemplate.LevelBoundsVerticalLimitHard;
        }

        private int GetLevelHorizontalBoundsSoft()
        {
            if (levelModel.Level == null)
            {
                return 1000;
            }

            var worldTemplate = voxelConfig.GetWorldTemplateOrDefault(levelModel.Level.worldTemplateId);

            return worldTemplate.LevelBoundsHorizontalLimitSoft;
        }

        private int GetLevelHorizontalBoundsHard()
        {
            if (levelModel.Level == null)
            {
                return 1000;
            }

            var worldTemplate = voxelConfig.GetWorldTemplateOrDefault(levelModel.Level.worldTemplateId);

            return worldTemplate.LevelBoundsHorizontalLimitHard;
        }
    }
}