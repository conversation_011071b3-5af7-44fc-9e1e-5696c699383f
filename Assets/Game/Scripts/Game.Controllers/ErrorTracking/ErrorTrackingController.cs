using System;
using Cysharp.Threading.Tasks;
using Game.Services;
using Game.Views.Levels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.ErrorTracking
{
    public class ErrorTrackingController : ControllerBase
    {
        private const string OutOfMemoryKey = "OutOfMemoryException";
        private const string BufferShouldBeNullKey = "Buffer should be null";

        private LevelModel levelModel;
        private INetworkClient networkClient;
        private IAnalyticsService analyticsService;

        private bool outOfMemoryErrorReceived;
        private bool bufferShouldBeNullErrorReceived;

        [Inject]
        private void Construct(LevelModel levelModel, INetworkClient networkClient, IAnalyticsService analyticsService)
        {
            this.levelModel = levelModel;
            this.networkClient = networkClient;
            this.analyticsService = analyticsService;

            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(DisposeCancellationToken);
            Application.logMessageReceived += HandleLogReceived;
        }

        public override void Dispose()
        {
            base.Dispose();
            Application.logMessageReceived -= HandleLogReceived;
        }

        private void HandleLogReceived(string condition, string stacktrace, LogType logType)
        {
            if (networkClient.IsConnected.Value && logType == LogType.Exception)
            {
                TrackOutOfMemoryError(condition);
                TrackBufferShouldBeNullError(condition);
            }
        }

        private void HandleShutdown()
        {
            outOfMemoryErrorReceived = false;
            bufferShouldBeNullErrorReceived = false;
        }

        private void TrackBufferShouldBeNullError(string condition)
        {
            if (!bufferShouldBeNullErrorReceived && condition.Contains(BufferShouldBeNullKey))
            {
                bufferShouldBeNullErrorReceived = true;

                var levelName = levelModel.Level.name;
                var roomId = networkClient.ShortSessionId;
                var playerCount = networkClient.PlayerCount.Value;
                var localTime = Mathf.RoundToInt(networkClient.LocalTime);
                var serverTime = Mathf.RoundToInt(networkClient.ServerTime);
                analyticsService.BufferShouldBeNullError(levelName, roomId, playerCount, localTime, serverTime);
            }
        }

        private void TrackOutOfMemoryError(string condition)
        {
            if (!outOfMemoryErrorReceived && condition.Contains(OutOfMemoryKey))
            {
                outOfMemoryErrorReceived = true;

                var levelName = levelModel.Level.name;
                var roomId = networkClient.ShortSessionId;
                var playerCount = networkClient.PlayerCount.Value;
                var localTime = Mathf.RoundToInt(networkClient.LocalTime);
                var serverTime = Mathf.RoundToInt(networkClient.ServerTime);
                analyticsService.OutOfMemoryErrorError(levelName, roomId, playerCount, localTime, serverTime);
            }
        }
    }
}