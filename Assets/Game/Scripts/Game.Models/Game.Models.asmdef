{"name": "Game.Models", "rootNamespace": "", "references": ["Modules.Core", "Modules.Analytics", "Modules.Oculus", "Modules.Network", "Modules.UI", "Modules.XR", "Game.Core", "Game.Views", "Game.Services", "Unity.XR.Interaction.Toolkit", "VoxelPlay", "MessagePipe", "MessagePipe.VContainer", "<PERSON><PERSON><PERSON>", "UniTask", "UniTask.Linq"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}