using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Game.Core.Data;
using Game.Views.Avatars;
using Game.Views.Interactables;
using Game.Views.Shared;
using JetBrains.Annotations;
using Modules.Core;
using Modules.XR;
using TMPro;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using XRSimpleInteractable = UnityEngine.XR.Interaction.Toolkit.Interactables.XRSimpleInteractable;

namespace Game.Views.BigShop
{
    public class InventoryView : XRSimpleInteractable
    {
        [SerializeField] private TMP_Text nameText;
        [SerializeField] private TMP_Text levelText;
        [SerializeField] private TMP_Text ownedText;
        [SerializeField] private TMP_Text infoText;
        [SerializeField] private GameObject uiNode;
        [SerializeField] private GameObject infoNode;
        [SerializeField] private Transform avatarNode;
        [SerializeField] private Transform interactableNode;
        [SerializeField] private Transform popupNode;
        [SerializeField] private Avatar<PERSON>rovider avatarProvider;
        [SerializeField] private InteractableProvider interactableProvider;
        [SerializeField] private BuyInventoryButton buyButton;
        [SerializeField] private float grabEndThreshold = 0.5f;
        [SerializeField] private List<MeshRenderer> rendererList;
        [SerializeField] private List<RarityMaterial> rarityMaterialList;

        private readonly ISubject<InventoryView> onClicked = new Subject<InventoryView>();

        private IXRInput xrInput;
        private IXRPlayer xrPlayer;
        private Tweener scaleTweener;
        private Transform currentNode;
        private Vector3 grabDeltaPosition;
        private Vector3 grabInitialPosition;
        private Vector3 originLocalPosition;
        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        public ShopItemData ShopItem { get; private set; }
        public Pose PopupPose => popupNode.GetPose();
        public UnityEngine.XR.Interaction.Toolkit.Interactors.IXRSelectInteractor GrabInteractor { get; private set; }
        public IObservable<InventoryView> OnClicked => onClicked;

        [Inject]
        private void Construct(IXRInput xrInput, IXRPlayer xrPlayer, DistanceCuller distanceCuller)
        {
            this.xrInput = xrInput;
            this.xrPlayer = xrPlayer;
            this.distanceCuller = distanceCuller;
        }

        protected override void Awake()
        {
            base.Awake();
            currentNode = avatarNode;
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            disableCancellationTokenSource = new CancellationTokenSource();
            buyButton.OnClicked.Subscribe(_ => Click()).AddTo(disableCancellationTokenSource.Token);
            xrPlayer.OnPoseUpdated.Subscribe(_ => UpdateGrabbing()).AddTo(disableCancellationTokenSource.Token);

            distanceCuller.AddTarget(uiNode);
            distanceCuller.AddTarget(infoText.gameObject);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ResetGrabbing();
            avatarNode.localScale = interactableNode.localScale = Vector3.one;
            disableCancellationTokenSource.CancelAndDispose();

            distanceCuller.RemoveTarget(uiNode);
            distanceCuller.RemoveTarget(infoText.gameObject);
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            if (!ShopItem.IsOwned)
            {
                return;
            }

            currentNode.DOScale(1.2f, 0.2f).SetEase(Ease.OutBack);
            xrInput.SendHapticImpulse(args.interactorObject.GetHandType(), HapticImpulse.ShortDurationLowAmplitude);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            if (!ShopItem.IsOwned)
            {
                return;
            }

            currentNode.DOScale(1, 0.2f).SetEase(Ease.OutBack);
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);
            if (!ShopItem.IsOwned)
            {
                return;
            }

            GrabInteractor = args.interactorObject;
            grabInitialPosition = GrabInteractor.transform.position;
            grabDeltaPosition = currentNode.position - grabInitialPosition;
        }

        protected override void OnSelectExited(SelectExitEventArgs args)
        {
            base.OnSelectExited(args);
            if (!ShopItem.IsOwned)
            {
                return;
            }

            ResetGrabbing();
        }

        public void Render(ShopItemData shopItem, Pose pose, Transform parent)
        {
            if (ShopItem == shopItem)
            {
                return;
            }

            Clear();

            if (shopItem == null)
            {
                return;
            }

            ShopItem = shopItem;

            if (ShopItem.HasInteractableOnly)
            {
                var isWing = ShopItem.FirstInventory.category == InventoryCategory.Wing;
                var scale = isWing ? 0.4f : 1f;
                interactableProvider.Create(ShopItem.FirstInventory.viewCode, scale);
                currentNode = interactableNode;
            }
            else
            {
                SetupAvatarAndOutfit();
                currentNode = avatarNode;
            }

            nameText.text = ShopItem.title;
            levelText.text = $"Level {ShopItem.FirstInventory.level}";
            infoText.text = ShopItem.description;
            rendererList.ForEach(r => r.sharedMaterial = rarityMaterialList.Find(x => x.rarity == ShopItem.Rarity)?.material);
            transform.SetParent(parent, true);
            transform.SetPose(pose);

            UpdateState();
        }

        public void UpdateState()
        {
            if (ShopItem == null)
            {
                return;
            }

            if (ShopItem.IsOwned)
            {
                if (ShopItem.HasAvatarOrOutfitOnly)
                {
                    ownedText.gameObject.SetActive(false);
                    buyButton.gameObject.SetActive(true);
                    buyButton.SetEquipText();
                }
                else
                {
                    buyButton.gameObject.SetActive(false);
                    ownedText.gameObject.SetActive(true);
                }

                infoNode.SetActive(!ShopItem.isLocked && ShopItem.HasDescription);
            }
            else
            {
                ownedText.gameObject.SetActive(false);
                buyButton.gameObject.SetActive(!ShopItem.isLocked);
                buyButton.SetBuyText(ShopItem.coinPrice.ToString());
                infoNode.SetActive(ShopItem.HasDescription);
            }
        }

        private void SetupAvatarAndOutfit()
        {
            foreach (var inventory in ShopItem.InventoryList)
            {
                avatarProvider.SetAvatarOrOutfit(inventory);
            }
        }

        private void Clear()
        {
            avatarProvider.ClearAvatar();
            interactableProvider.Destroy();
        }

        [UsedImplicitly]
        private void Click()
        {
            if (ShopItem == null)
            {
                return;
            }

            onClicked.OnNext(this);
        }

        private void UpdateGrabbing()
        {
            if (GrabInteractor == null)
            {
                return;
            }

            var grabCurrentPosition = GrabInteractor.transform.position;
            var distance = Vector3.Distance(grabInitialPosition, grabCurrentPosition);

            if (distance > grabEndThreshold)
            {
                Click();
                ResetGrabbing();
            }
            else
            {
                currentNode.position = grabCurrentPosition + grabDeltaPosition;
            }
        }

        private void ResetGrabbing()
        {
            GrabInteractor = null;
            currentNode.localPosition = Vector3.zero;
        }

        [Serializable]
        private class RarityMaterial
        {
            public InventoryRarity rarity;
            public Material material;
        }
    }
}