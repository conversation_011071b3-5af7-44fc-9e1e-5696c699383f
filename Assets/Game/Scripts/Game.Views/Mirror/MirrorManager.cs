using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Mirror
{
    public class MirrorManager : MonoBehaviour
    {
        [SerializeField] private MirrorView mirrorViewPrefab;

        private IObjectResolver objectResolver;
        private ComponentPool<MirrorView> mirrorPool;

        private ComponentPool<MirrorView> MirrorViewPool => mirrorPool ??= new ComponentPool<MirrorView>(mirrorViewPrefab, transform, objectResolver, 1);

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public MirrorView Create(Pose pose)
        {
            var view = MirrorViewPool.Get();
            view.SetPose(pose);
            return view;
        }

        public void DestroyAll()
        {
            MirrorViewPool.ReleaseAll();
        }
    }
}