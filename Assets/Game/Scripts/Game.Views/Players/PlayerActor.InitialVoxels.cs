using Cysharp.Threading.Tasks;
using Fusion;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<bool> isInitialVoxelsReceived = new AsyncReactiveProperty<bool>(false);

        [Networked] [OnChangedRender(nameof(ChangeIsInitialVoxelsReceivedNetwork))]
        private NetworkBool IsInitialVoxelsReceivedNetwork { get; set; }

        public IReadOnlyAsyncReactiveProperty<bool> IsInitialVoxelsReceived => isInitialVoxelsReceived;

        public void SetInitialVoxelsReceived(bool isInitialVoxelsReceivedNetwork)
        {
            IsInitialVoxelsReceivedNetwork = isInitialVoxelsReceivedNetwork;
        }

        private void ChangeIsInitialVoxelsReceivedNetwork()
        {
            if (isInitialVoxelsReceived.Value != IsInitialVoxelsReceivedNetwork)
            {
                isInitialVoxelsReceived.Value = IsInitialVoxelsReceivedNetwork;
            }
        }
    }
}