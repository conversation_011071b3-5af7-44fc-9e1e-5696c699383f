using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Modules.XR;

namespace Game.Views.Players
{
    public class LocalFingerPosesProvider : IFingerPosesProvider
    {
        private readonly IXRInput xrInput;
        private readonly IAsyncReactiveProperty<FingerPoses> fingerPoses = new AsyncReactiveProperty<FingerPoses>(Core.Data.FingerPoses.None);

        public IReadOnlyAsyncReactiveProperty<FingerPoses> FingerPoses => fingerPoses;

        public LocalFingerPosesProvider(IXRInput xrInput)
        {
            this.xrInput = xrInput;
        }

        public void Update()
        {
            fingerPoses.Value = GetCurrent();
        }

        private FingerPoses GetCurrent()
        {
            FingerPoses current = default;

            if (xrInput.HasLeftHandSelection)
            {
                current |= Core.Data.FingerPoses.LeftIndex;
                current |= Core.Data.FingerPoses.LeftPinky;
                current |= Core.Data.FingerPoses.LeftThumb;
            }
            else
            {
                if (xrInput.IsLeftActivatePressed)
                {
                    current |= Core.Data.FingerPoses.LeftIndex;
                }

                if (xrInput.IsLeftSelectPressed)
                {
                    current |= Core.Data.FingerPoses.LeftPinky;
                }

                if (xrInput.IsXButtonPressed)
                {
                    current |= Core.Data.FingerPoses.LeftThumb;
                }
            }

            if (xrInput.HasRightHandSelection)
            {
                current |= Core.Data.FingerPoses.RightIndex;
                current |= Core.Data.FingerPoses.RightPinky;
                current |= Core.Data.FingerPoses.RightThumb;
            }
            else
            {
                if (xrInput.IsRightActivatePressed)
                {
                    current |= Core.Data.FingerPoses.RightIndex;
                }

                if (xrInput.IsRightSelectPressed)
                {
                    current |= Core.Data.FingerPoses.RightPinky;
                }

                if (xrInput.IsAButtonPressed)
                {
                    current |= Core.Data.FingerPoses.RightThumb;
                }
            }

            return current;
        }
    }
}