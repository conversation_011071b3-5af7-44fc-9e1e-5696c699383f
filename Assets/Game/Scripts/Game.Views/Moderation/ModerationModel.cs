using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Game.Core;
using Modules.Core;

namespace Game.Views.Moderation
{
    public class ModerationModel : ModelBase
    {
        private readonly ISubject<ModerationApplyArgs> onModerationApplied = new Subject<ModerationApplyArgs>();
        private readonly ISubject<ModerationSentenceArgs> onModerationSentenceApplied = new Subject<ModerationSentenceArgs>();
        private readonly Dictionary<HandType, ModerationSentence> handToActiveSentence = new();

        public DateTimer MuteEndTimer { get; } = new();
        public DateTimer PrisonEndTimer { get; } = new();
        public int FineAmount => 1000 * ((int)PrisonEndTimer.RemainingTime.Value.TotalMinutes + 1);
        public List<ModerationSentence> AllowedSentences { get; } = new();
        public IObservable<ModerationApplyArgs> OnModerationApplied => onModerationApplied;
        public IObservable<ModerationSentenceArgs> OnModerationSentenceApplied => onModerationSentenceApplied;

        public override void Dispose()
        {
            base.Dispose();
            MuteEndTimer.Dispose();
            PrisonEndTimer.Dispose();
        }

        public void ApplyModeration(ModerationApplyArgs args)
        {
            onModerationApplied.OnNext(args);
        }

        public void ApplyModeration(ModerationSentenceArgs args)
        {
            onModerationSentenceApplied.OnNext(args);
        }

        public void SetAllowedSentences(List<ModerationSentence> allowedSentences)
        {
            AllowedSentences.Clear();
            AllowedSentences.AddRange(allowedSentences);
        }

        public void SetActiveSentence(HandType handType, ModerationSentence sentence)
        {
            handToActiveSentence[handType] = sentence;
        }

        public ModerationSentence GetActiveSentence(HandType handType)
        {
            return handToActiveSentence.GetValueOrDefault(handType, AllowedSentences.Count > 0 ? AllowedSentences[0] : ModerationSentence.None);
        }

        public ModerationSentence GetNextSentence(HandType handType)
        {
            if (AllowedSentences.Count == 0)
            {
                return ModerationSentence.None;
            }

            var activeSentence = GetActiveSentence(handType);
            var index = (AllowedSentences.IndexOf(activeSentence) + 1) % AllowedSentences.Count;
            return AllowedSentences[index];
        }
    }
}