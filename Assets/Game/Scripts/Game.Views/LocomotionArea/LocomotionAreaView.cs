using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core.Data;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;

namespace Game.Views.LocomotionArea
{
    public class LocomotionAreaView : Actor
    {
        [SerializeField] private XRPlayerDetector playerDetector;
        [SerializeField] private LineRenderer lineRenderer;

        private readonly ISubject<LocomotionAreaArgs> onPlayerDetected = new Subject<LocomotionAreaArgs>();

        private CancellationTokenSource disableCancellationTokenSource = new();
        private DisabledLocomotions disabledLocomotions;

        public IObservable<LocomotionAreaArgs> OnPlayerDetected => onPlayerDetected;

        public void Init(Vector3 position, Vector3 size, DisabledLocomotions disabledLocomotions)
        {
            transform.position = position;
            transform.localScale = size;
            this.disabledLocomotions = disabledLocomotions;
            DrawBounds(size);
        }

        private void DrawBounds(Vector3 size)
        {
            var halfSize = size * 0.5f;

            var corners = new Vector3[8];
            corners[0] = transform.position + new Vector3(-halfSize.x, -halfSize.y, -halfSize.z); // Bottom-back-left
            corners[1] = transform.position + new Vector3(halfSize.x, -halfSize.y, -halfSize.z); // Bottom-back-right
            corners[2] = transform.position + new Vector3(halfSize.x, -halfSize.y, halfSize.z); // Bottom-front-right
            corners[3] = transform.position + new Vector3(-halfSize.x, -halfSize.y, halfSize.z); // Bottom-front-left

            corners[4] = transform.position + new Vector3(-halfSize.x, halfSize.y, -halfSize.z); // Top-back-left
            corners[5] = transform.position + new Vector3(halfSize.x, halfSize.y, -halfSize.z); // Top-back-right
            corners[6] = transform.position + new Vector3(halfSize.x, halfSize.y, halfSize.z); // Top-front-right
            corners[7] = transform.position + new Vector3(-halfSize.x, halfSize.y, halfSize.z); // Top-front-left

            Vector3[] edges =
            {
                // Bottom square
                corners[0], corners[1],
                corners[1], corners[2],
                corners[2], corners[3],
                corners[3], corners[0],

                corners[0], corners[4], // vert up

                // Top square
                corners[4], corners[5],
                corners[5], corners[6],
                corners[6], corners[7],
                corners[7], corners[4],

                corners[4], corners[5], // hor
                corners[5], corners[1], // vert down

                corners[1], corners[2], // hor
                corners[2], corners[6], // vert up

                corners[6], corners[7], // hor
                corners[7], corners[3] // vert down
            };

            lineRenderer.loop = false;
            lineRenderer.positionCount = edges.Length;
            lineRenderer.SetPositions(edges);
        }

        private void OnEnable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();
            playerDetector.IsDetected.Subscribe(detected =>
            {
                if (detected)
                {
                    onPlayerDetected.OnNext(new LocomotionAreaArgs(this, disabledLocomotions));
                }
                else
                {
                    onPlayerDetected.OnNext(new LocomotionAreaArgs(this, DisabledLocomotions.None));
                }
            }).AddTo(disableCancellationTokenSource.Token);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
        }
    }
}