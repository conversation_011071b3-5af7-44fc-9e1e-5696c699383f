using UnityEngine;

namespace Game.Views.Voxels
{
    public readonly struct DestroyingVoxelArgs
    {
        public readonly Vector3 position;
        public readonly int time;
        public readonly bool byLocalPlayer;

        public DestroyingVoxelArgs(Vector3 position, int time, bool byLocalPlayer)
        {
            this.position = position;
            this.time = time;
            this.byLocalPlayer = byLocalPlayer;
        }
    }
}