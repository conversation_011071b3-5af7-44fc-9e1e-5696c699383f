using System.Collections.Generic;
using UnityEngine;
using VoxelPlay;

namespace Game.Views.Voxels
{
    public class VoxelModelPlacer : MonoBehaviour
    {
        [SerializeField] private ModelDefinition modelToPlace;
        [SerializeField] private VoxelDefinition emptyVoxel;

        private VoxelPlayEnvironment Voxel => VoxelPlayEnvironment.instance;

        private void OnEnable()
        {
            Voxel.OnWorldLoaded += OnWorldLoaded;
        }

        private void OnDisable()
        {
            if (Voxel == null)
            {
                return;
            }

            Voxel.OnWorldLoaded -= OnWorldLoaded;
        }

        public void OnWorldLoaded()
        {
            var voxels = new List<VoxelIndex>();
            Voxel.ModelPlace(transform.position, modelToPlace, 0, 0, false, voxels);

            foreach (var voxelIndex in voxels)
            {
                if (voxelIndex.type == emptyVoxel)
                {
                    Voxel.VoxelDestroy(voxelIndex.chunk, voxelIndex.voxelIndex);
                }
            }
        }
    }
}