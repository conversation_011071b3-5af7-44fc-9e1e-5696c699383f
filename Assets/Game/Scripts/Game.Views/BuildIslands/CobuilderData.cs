namespace Game.Views.BuildIslands
{
    public class CobuilderData
    {
        public readonly int playerId;
        public readonly string playerName;
        public readonly bool isOwner;
        public readonly bool isLocalPlayer;
        public readonly bool isRequest;

        public CobuilderData(int playerId, string playerName, bool isOwner, bool isLocalPlayer, bool isRequest)
        {
            this.playerId = playerId;
            this.playerName = playerName;
            this.isOwner = isOwner;
            this.isLocalPlayer = isLocalPlayer;
            this.isRequest = isRequest;
        }
    }
}