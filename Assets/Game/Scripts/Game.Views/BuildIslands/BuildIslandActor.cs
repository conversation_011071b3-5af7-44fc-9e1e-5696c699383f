using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Core;
using Game.Views.Players;
using Game.Views.Shared;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.BuildIslands
{
    public class BuildIslandActor : NetworkActor
    {
        private const int MaxCobuilderCount = 6;

        [SerializeField] private BoxCollider buildZoneTrigger;
        [SerializeField] private BuildIslandScreen buildIslandScreen;

        private readonly List<CobuilderData> cobuilderDataList = new();
        private readonly ISubject<BuildIslandActor> onClearClicked = new Subject<BuildIslandActor>();
        private readonly ISubject<BuildIslandActor> onResetClicked = new Subject<BuildIslandActor>();
        private readonly ISubject<BuildIslandActor> onSaveClicked = new Subject<BuildIslandActor>();
        private readonly ISubject<BuildIslandActor> onLoadClicked = new Subject<BuildIslandActor>();
        private readonly ISubject<BuildIslandActor> onClaimRequesting = new Subject<BuildIslandActor>();

        private GameTimer loadClickTimer;
        private PlayersModel playersModel;
        private DistanceCuller distanceCuller;
        private INetworkClient networkClient;

        public Pose OriginPose => transform.GetPose();
        public Bounds Bounds => buildZoneTrigger.bounds;
        public IObservable<BuildIslandActor> OnClearClicked => onClearClicked;
        public IObservable<BuildIslandActor> OnResetClicked => onResetClicked;
        public IObservable<BuildIslandActor> OnSaveClicked => onSaveClicked;
        public IObservable<BuildIslandActor> OnLoadClicked => onLoadClicked;
        public IObservable<BuildIslandActor> OnClaimRequesting => onClaimRequesting;

        [Networked] [OnChangedRender(nameof(ChangeOwner))]
        public PlayerRef Owner { get; private set; }

        [Networked] [Capacity(MaxCobuilderCount)] [OnChangedRender(nameof(ChangeCobuilderList))]
        private NetworkLinkedList<int> CobuilderList { get; } = default;

        private PlayerRef LocalPlayerRef => Runner.LocalPlayer;
        private bool IsOwner => Owner == LocalPlayerRef;
        private bool NoOwner => Owner == PlayerRef.None;

        [Inject]
        private void Construct(PlayersModel playersModel, INetworkClient networkClient, DistanceCuller distanceCuller)
        {
            this.playersModel = playersModel;
            this.networkClient = networkClient;
            this.distanceCuller = distanceCuller;
        }

        public override void Spawned()
        {
            base.Spawned();

            UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(2)).Subscribe(_ =>
            {
                ChangeOwner();
                ChangeCobuilderList();
            }).AddTo(destroyCancellationToken);

            buildIslandScreen.ClaimButton.OnClicked.Subscribe(HandleClaimClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.UnclaimButton.OnClicked.Subscribe(HandleUnclaimClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.SaveButton.OnClicked.Subscribe(HandleSaveClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.LoadButton.OnClicked.Subscribe(HandleLoadClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.ResetButton.OnClicked.Subscribe(HandleClearClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.CobuilderRequestButton.OnClicked.Subscribe(HandleRequestClicked).AddTo(DespawnCancellationToken);
            buildIslandScreen.OnCobuilderAdded.Subscribe(HandlePlayerAdded).AddTo(DespawnCancellationToken);
            buildIslandScreen.OnCobuilderRemoved.Subscribe(HandlePlayerRemoved).AddTo(DespawnCancellationToken);

            distanceCuller.AddTarget(buildIslandScreen.UiNode, Constants.UiCullDistance);
            distanceCuller.AddTarget(buildIslandScreen.gameObject, Constants.ObjectCullDistance);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            distanceCuller.RemoveTarget(buildIslandScreen.UiNode);
            distanceCuller.RemoveTarget(buildIslandScreen.gameObject);
        }

        public void SetLoadClickTimer(GameTimer loadClickTimer)
        {
            this.loadClickTimer = loadClickTimer;
            loadClickTimer.OnRemainingTimeUpdated.Subscribe(HandleLoadClickTimerRemainingTime).AddTo(destroyCancellationToken);
        }

        private void HandleLoadClickTimerRemainingTime(float time)
        {
            buildIslandScreen.LoadButton.SetTitle($"load ({Mathf.RoundToInt(time)})");
        }

        public bool IsOpenedBuildZone(Vector3 point)
        {
            return Bounds.Contains(point) && (NoOwner || LocalPlayerRef == Owner || CobuilderList.Contains(LocalPlayerRef.PlayerId));
        }

        public bool IsClosedBuildZone(Vector3 point)
        {
            return Bounds.Contains(point) && !NoOwner && !(LocalPlayerRef == Owner || CobuilderList.Contains(LocalPlayerRef.PlayerId));
        }

        public void Claim()
        {
            if (NoOwner)
            {
                ClaimBuildZoneRpc();
            }
        }

        public void Clear()
        {
            ClearBuildZoneRpc();
        }

        public void CheckOwnersAndCobuilders()
        {
            if (NoOwner)
            {
                return;
            }

            if (networkClient.HasPlayer(Owner))
            {
                for (var i = 0; i < CobuilderList.Count; i++)
                {
                    var cobuilder = CobuilderList[i];
                    if (!networkClient.HasPlayer(cobuilder))
                    {
                        CobuilderList.Remove(cobuilder);
                        i--;
                    }
                }
            }
            else
            {
                Owner = PlayerRef.None;
                ClearCobuilderList();
            }
        }

        private void HandleClaimClicked(Unit unit)
        {
            buildIslandScreen.ActivateLoading();
            onClaimRequesting.OnNext(this);
        }

        private void HandleUnclaimClicked(Unit unit)
        {
            if (IsOwner)
            {
                buildIslandScreen.ActivateLoading();
                UnclaimBuildZoneRpc();
            }
        }

        private void HandleSaveClicked(Unit unit)
        {
            buildIslandScreen.ActivateLoading();
            onSaveClicked.OnNext(this);
        }

        private void HandleLoadClicked(Unit unit)
        {
            if (loadClickTimer.IsRun)
            {
                return;
            }

            loadClickTimer.Start(60, 1);
            buildIslandScreen.ActivateLoading();
            onLoadClicked.OnNext(this);
        }

        private void HandleClearClicked(Unit unit)
        {
            buildIslandScreen.ActivateLoading();
            ResetBuildZoneRpc();
        }

        private void HandleRequestClicked(Unit unit)
        {
            buildIslandScreen.ActivateLoading();
            RequestCobuilderRpc(Owner);
        }

        private void HandlePlayerAdded(int playerId)
        {
            if (IsOwner && CobuilderList.Count < MaxCobuilderCount && !CobuilderList.Contains(playerId))
            {
                buildIslandScreen.ActivateLoading();
                AddCobuilderRpc((short)playerId);
            }
        }

        private void HandlePlayerRemoved(int playerId)
        {
            buildIslandScreen.ActivateLoading();

            if (CobuilderList.Contains(playerId))
            {
                RemoveCobuilderRpc((short)playerId);
            }
            else if (IsOwner)
            {
                cobuilderDataList.RemoveAll(c => c.playerId == playerId);
                buildIslandScreen.RenderCobuilders(cobuilderDataList);
            }
        }

        private void ChangeOwner()
        {
            buildIslandScreen.ActivateLoading();

            if (!NoOwner && playersModel.TryGetPlayer(Owner, out var player))
            {
                if (IsOwner)
                {
                    buildIslandScreen.ActivateOwnerNode(player.Name.Value);
                }
                else
                {
                    buildIslandScreen.ActivateBlockNode(player.Name.Value);
                }
            }
            else
            {
                buildIslandScreen.ActivateFreeNode();
            }
        }

        private void ChangeCobuilderList()
        {
            cobuilderDataList.Clear();
            foreach (var cobuilder in CobuilderList)
            {
                if (playersModel.TryGetPlayer(cobuilder, out var player) && !cobuilderDataList.Exists(c => c.playerId == cobuilder))
                {
                    var isLocalPlayer = cobuilder == LocalPlayerRef.PlayerId;
                    cobuilderDataList.Add(new CobuilderData(cobuilder, player.Name.Value, IsOwner, isLocalPlayer, false));
                }
            }

            buildIslandScreen.RenderCobuilders(cobuilderDataList);

            var isCobuilderRequestButtonActive = !IsOwner && !NoOwner && !cobuilderDataList.Exists(c => c.playerId == LocalPlayerRef.PlayerId);
            buildIslandScreen.CobuilderRequestButton.SetActive(isCobuilderRequestButtonActive);
        }

        private void ClearCobuilderList()
        {
            for (var i = 0; i < CobuilderList.Count; i++)
            {
                CobuilderList.Remove(CobuilderList[i]);
                i--;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void ClaimBuildZoneRpc(RpcInfo info = default)
        {
            if (NoOwner)
            {
                Owner = info.Source;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void UnclaimBuildZoneRpc(RpcInfo info = default)
        {
            if (Owner == info.Source)
            {
                Owner = PlayerRef.None;
                ClearCobuilderList();
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void ResetBuildZoneRpc()
        {
            onResetClicked.OnNext(this);
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void ClearBuildZoneRpc()
        {
            onClearClicked.OnNext(this);
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void RequestCobuilderRpc([RpcTarget] PlayerRef target, RpcInfo info = default)
        {
            if (Owner == PlayerRef.None
                || Owner != target
                || CobuilderList.Count >= MaxCobuilderCount
                || cobuilderDataList.Exists(c => c.playerId == info.Source.PlayerId)
                || !playersModel.TryGetPlayer(info.Source.PlayerId, out var player))
            {
                return;
            }

            cobuilderDataList.Insert(0, new CobuilderData(info.Source.PlayerId, player.Name.Value, IsOwner, false, true));
            buildIslandScreen.RenderCobuilders(cobuilderDataList);
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void AddCobuilderRpc(short cobuilderId)
        {
            if (NoOwner || CobuilderList.Count >= MaxCobuilderCount || CobuilderList.Contains(cobuilderId))
            {
                return;
            }

            CobuilderList.Add(cobuilderId);
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void RemoveCobuilderRpc(short cobuilderId)
        {
            if (NoOwner || !CobuilderList.Contains(cobuilderId))
            {
                return;
            }

            CobuilderList.Remove(cobuilderId);
        }
    }
}