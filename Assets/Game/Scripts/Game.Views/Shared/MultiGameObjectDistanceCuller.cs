using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;

namespace Game.View.Shared
{
    public class MultiGameObjectDistanceCuller : MonoBehaviour
    {
        [SerializeField] private GameObject[] targetObjects;
        [SerializeField] private float cullDistance = 50f;
        [SerializeField] private float updateIntervalMilliseconds = 1000f;
        private Camera mainCamera;
        private CancellationTokenSource cancellationTokenSource;

        private void OnEnable()
        {
            cancellationTokenSource = new CancellationTokenSource();
            UpdateDistanceCulling(cancellationTokenSource).Forget();
        }

        private async UniTask UpdateDistanceCulling(CancellationTokenSource token)
        {
            await UniTask.Delay(TimeSpan.FromMilliseconds(4000), cancellationToken: token.Token); // Wait to disable any other cameras
            while (!token.IsCancellationRequested)
            {
                if (mainCamera == null) mainCamera = Camera.main;
                if (mainCamera != null)
                {
                    foreach (var targetObject in targetObjects)
                    {
                        if (targetObject == null) continue;
                        var distance = DistanceXYZ(mainCamera.transform.position, targetObject.transform.position);
                        targetObject.SetActive(distance < cullDistance);
                    }

                    await UniTask.Delay(TimeSpan.FromMilliseconds(updateIntervalMilliseconds), cancellationToken: token.Token);
                }
                else
                {
                    await UniTask.Delay(TimeSpan.FromMilliseconds(1000), cancellationToken: token.Token);
                }
            }
        }

        private float DistanceXYZ(Vector3 a, Vector3 b)
        {
            var xDiff = a.x - b.x;
            var yDiff = a.y - b.y;
            var zDiff = a.z - b.z;
            return xDiff * xDiff + yDiff * yDiff + zDiff * zDiff;
        }

        private void OnDisable() => cancellationTokenSource.CancelAndDispose();
        private void OnDestroy() => cancellationTokenSource.CancelAndDispose();
    }
}