using UnityEngine;

namespace Game.Views.Avatars
{
    public readonly struct AvatarHandPose
    {
        public readonly Vector3 wristPosition;
        public readonly Vector3 elbowPosition;
        public readonly Vector3 shoulderPosition;
        public readonly Quaternion wristLocalRotation;

        public AvatarHandPose(Vector3 wristPosition, Vector3 elbowPosition, Vector3 shoulderPosition, Quaternion wristLocalRotation)
        {
            this.wristPosition = wristPosition;
            this.elbowPosition = elbowPosition;
            this.shoulderPosition = shoulderPosition;
            this.wristLocalRotation = wristLocalRotation;
        }
    }
}