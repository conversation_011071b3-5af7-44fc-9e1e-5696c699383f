using System.Collections.Generic;
using Game.Core;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Avatars
{
    public class AvatarsConfig : Config
    {
        [SerializeField] private string defaultAvatarCode;
        [SerializeField] private AvatarView avatarViewPrefab;

        [Header("Data")]
        [SerializeField] private List<AvatarData> avatarList;
        [SerializeField] private List<AvatarData> suitList;
        [SerializeField] private List<AvatarData> hatList;

        public string DefaultAvatarCode => defaultAvatarCode;
        public byte DefaultAvatarId => TryGetAvatarId(DefaultAvatarCode, out var id) ? (byte)id : (byte)0;
        public AvatarView AvatarViewPrefab => avatarViewPrefab;
        public List<AvatarData> AvatarList => avatarList;
        public List<AvatarData> SuitList => suitList;
        public List<AvatarData> HatList => hatList;

        public void AddAvatar(string code)
        {
            avatarList.Add(new AvatarData { Code = code });
        }

        public void AddSuit(string code)
        {
            suitList.Add(new AvatarData { Code = code });
        }

        public void AddHat(string code)
        {
            hatList.Add(new AvatarData { Code = code });
        }

        public bool TryGetAvatarId(string code, out int id)
        {
            var data = avatarList.Find(x => x.Code == code);
            id = data?.Id ?? AvatarView.NullItemId;
            return data != null;
        }

        public bool TryGetAvatarCode(int id, out string code)
        {
            var data = avatarList.Find(x => x.Id == id);
            code = data?.Code;
            return data != null;
        }

        public bool TryGetSuitId(string code, out int id)
        {
            var data = suitList.Find(x => x.Code == code);
            id = data?.Id ?? AvatarView.NullItemId;
            return data != null;
        }

        public bool TryGetSuitCode(int id, out string code)
        {
            var data = suitList.Find(x => x.Id == id);
            code = data?.Code;
            return data != null;
        }

        public bool TryGetHatId(string code, out int id)
        {
            var data = hatList.Find(x => x.Code == code);
            id = data?.Id ?? AvatarView.NullItemId;
            return data != null;
        }

        public bool TryGetHatCode(int id, out string code)
        {
            var data = hatList.Find(x => x.Id == id);
            code = data?.Code;
            return data != null;
        }

        public bool HasAvatar(int id)
        {
            return avatarList.Exists(x => x.Id == id);
        }

        public bool HasAvatar(string code)
        {
            return avatarList.Exists(x => x.Code == code);
        }

        public bool HasHat(int id)
        {
            return hatList.Exists(x => x.Id == id);
        }

        public bool HasHat(string code)
        {
            return hatList.Exists(x => x.Code == code);
        }

        public bool HasSuit(int id)
        {
            return suitList.Exists(x => x.Id == id);
        }

        public bool HasSuit(string code)
        {
            return suitList.Exists(x => x.Code == code);
        }

        public bool IsAminMonster(string code)
        {
            return code == InventoryCodes.Persuer || code == InventoryCodes.GoatMan || code == InventoryCodes.Mimicer;
        }
    }
}