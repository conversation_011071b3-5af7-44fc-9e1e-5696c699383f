using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;

namespace Game.Views.Extensions
{
    public static class NetworkExtensions
    {
        public static async UniTask HasPlayerObjectAsync(this NetworkRunner runner, PlayerRef player, CancellationToken cancellationToken)
        {
            await UniTask.WaitUntil(() => runner != null && runner.TryGetPlayerObject(player, out _), cancellationToken: cancellationToken);
        }
    }
}