using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Views.Shared;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Atm
{
    public class AtmManager : Actor
    {
        [SerializeField] private List<AtmView> atmViewList;

        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        private readonly ISubject<PurchaseClickArgs> onPurchaseClicked = new Subject<PurchaseClickArgs>();

        public IObservable<PurchaseClickArgs> OnPurchaseClicked => onPurchaseClicked;

        [Inject]
        private void Construct(DistanceCuller distanceCuller)
        {
            this.distanceCuller = distanceCuller;
        }

        private void OnEnable()
        {
            disableCancellationTokenSource = new CancellationTokenSource();
            atmViewList.ForEach(a => a.OnPurchaseClicked.Subscribe(onPurchaseClicked.OnNext).AddTo(disableCancellationTokenSource.Token));
            atmViewList.ForEach(a => distanceCuller.AddTarget(a.gameObject, Constants.ObjectCullDistance));
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            atmViewList.ForEach(a => distanceCuller.RemoveTarget(a.gameObject));
        }

        public void Initialize(List<ShopItemData> coinItemList)
        {
            atmViewList.ForEach(s => s.Initialize(coinItemList));
        }

        public void SetCoinAmount(int coinAmount)
        {
            atmViewList.ForEach(s => s.SetCoinAmount(coinAmount));
        }
    }
}