using System;
using Game.Core.Data;
using UnityEngine;

namespace Game.Views.GadgetShop
{
    [Serializable]
    public class GadgetShopData
    {
        public string viewCode;
        public string title;
        public int diamondPrice;
        public int diamondReward;
        [Range(0f, 1f)] public float rewardProbability;
        public InventoryCategory category;
        public bool disabledInShop;

        public override string ToString()
        {
            return title;
        }
    }
}