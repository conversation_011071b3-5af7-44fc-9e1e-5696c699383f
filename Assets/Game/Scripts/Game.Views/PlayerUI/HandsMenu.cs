using System;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Game.Views.Avatars;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;

namespace Game.Views.PlayerUI
{
    public class HandsMenu : Actor
    {
        [SerializeField] private LeftHandMenu leftHandMenu;
        [SerializeField] private RightHandMenu rightHandMenu;

        private readonly ISubject<InteractableWidget> onClicked = new Subject<InteractableWidget>();
        private readonly ISubject<InteractableWidget> onGrabbed = new Subject<InteractableWidget>();
        private readonly ISubject<InteractableWidget> onMoneyBagSpawned = new Subject<InteractableWidget>();

        public IObservable<InteractableWidget> OnGrabbed => onGrabbed;
        public IObservable<InteractableWidget> OnClicked => onClicked;
        public IObservable<Unit> OnHomeClicked => leftHandMenu.OnHomeClicked;
        public IObservable<Unit> OnGoToCheckpointClicked => leftHandMenu.OnGoToCheckpointClicked;
        public IObservable<Unit> OnInteractablesMenuClicked => rightHandMenu.OnInteractablesMenuClicked;
        public IObservable<InteractableWidget> OnMoneyBagMenuClicked => rightHandMenu.OnMoneyBagMenuClicked;
        public IObservable<InteractableWidget> OnMoneyBagSpawned => onMoneyBagSpawned;
        public InteractableWidget ActiveInteractableWidget { get; private set; }

        private void Start()
        {
            leftHandMenu.OnGrabbed.Subscribe(HandleGrabbed).AddTo(destroyCancellationToken);
            leftHandMenu.OnClicked.Subscribe(HandleClicked).AddTo(destroyCancellationToken);

            rightHandMenu.OnGrabbed.Subscribe(HandleGrabbed).AddTo(destroyCancellationToken);
            rightHandMenu.OnClicked.Subscribe(HandleClicked).AddTo(destroyCancellationToken);
            rightHandMenu.OnInteractablesMenuClicked.Subscribe(_ => ResetActiveInteractableWidget()).AddTo(destroyCancellationToken);
        }

        public void UpdatePose(AvatarHandPose leftHandPose, AvatarHandPose rightHandPose)
        {
            leftHandMenu.UpdatePose(leftHandPose.wristPosition, leftHandPose.elbowPosition, leftHandPose.wristLocalRotation);
            rightHandMenu.UpdatePose(rightHandPose.wristPosition, rightHandPose.elbowPosition, rightHandPose.wristLocalRotation);
        }

        public void SetActiveInteractableList(ActiveInteractableList activeInteractableList)
        {
            foreach (var activeInteractable in activeInteractableList)
            {
                if (activeInteractable.hand == HandType.Left)
                {
                    leftHandMenu.SetActiveInteractable(activeInteractable);
                }
                else
                {
                    rightHandMenu.SetActiveInteractable(activeInteractable);
                }
            }
        }

        public InteractableWidget GetEmptyWidget()
        {
            return leftHandMenu.GetEmptyWidget() ?? rightHandMenu.GetEmptyWidget();
        }

        public void ResetActiveInteractableWidget()
        {
            ActiveInteractableWidget = null;
        }

        public void SetScale(float scale)
        {
            leftHandMenu.SetScale(scale);
            rightHandMenu.SetScale(scale);
        }

        public void SpawnMoneyBag(InteractableWidget widget)
        {
            onMoneyBagSpawned.OnNext(widget);
        }

        public void SetMoneyBagActive(bool isActive) => rightHandMenu.SetMoneyBagActive(isActive);
        public void SetBalanceText(int balance) => rightHandMenu.SetBalanceText(balance);
        public void SetHomeWidgetActive(bool isActive) => leftHandMenu.SetHomeWidgetActive(isActive);
        public void SetInteractableWidgetActive(bool isActive) => rightHandMenu.SetInteractableWidgetActive(isActive);

        public void SetInteractableWidgetNodeActive(bool active)
        {
            leftHandMenu.SetInteractableWidgetNodeActive(active);
            rightHandMenu.SetInteractableWidgetNodeActive(active);
        }

        private void HandleGrabbed(InteractableWidget widget) => onGrabbed.OnNext(widget);

        private void HandleClicked(InteractableWidget widget)
        {
            ActiveInteractableWidget = widget;
            onClicked.OnNext(widget);
        }

        public void ToggleCheckpointButton(bool isEnabled)
        {
            leftHandMenu.ToggleCheckpointButton(isEnabled);
        }
    }
}