using Game.Core;
using Game.Views.Blocks;
using Game.Views.Shared;
using Modules.UI;
using VContainer;

namespace Game.Views.PlayerUI.Blocks
{
    public class BlockWidget : BubbleWidgetBase<BlockWidget>
    {
        private BlockView blockView;
        private BlocksManager blocksManager;

        protected override bool CanCallClickWhenGrabbing => true;
        public int Id { get; private set; }

        [Inject]
        private void Construct(BlocksManager blocksManager)
        {
            this.blocksManager = blocksManager;
        }

        public void SetBlock(BlockWidgetData data, bool useBlockCountText, float scale = 0.05f)
        {
            ClearBlock();
            Id = data.id;

            if (data.id >= 0)
            {
                blockView = blocksManager.CreateBlock(data.id, true, useBlockCountText, ViewNode);
                if (blockView != null)
                {
                    blockView.SetScale(scale);
                    blockView.SetBlockCount(data.count);
                }
            }
        }

        public void ClearBlock()
        {
            if (blockView == null)
            {
                return;
            }

            blocksManager.DestroyBlock(blockView);
            blockView = null;
        }

        public void SetBlockCount(int blockCount)
        {
            blockView.SetBlockCount(blockCount);
        }

        protected override void CallClicked()
        {
            base.CallClicked();
            AudioClient.Play(UIAudioKeys.ButtonClick, transform.position, destroyCancellationToken);
            onClicked.OnNext(this);
        }

        protected override void CallGrabbed()
        {
            base.CallGrabbed();
            AudioClient.Play(AudioKeys.Goal, transform.position, destroyCancellationToken);
            onGrabbed.OnNext(this);
        }
    }
}