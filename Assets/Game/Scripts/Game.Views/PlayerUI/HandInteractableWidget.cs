using Modules.Core;
using Modules.XR;

namespace Game.Views.PlayerUI
{
    public class HandInteractableWidget : InteractableWidget
    {
        public override bool IsHoverableBy(UnityEngine.XR.Interaction.Toolkit.Interactors.IXRHoverInteractor interactor)
        {
            return base.IsHoverableBy(interactor) && !IsSameHand(interactor);
        }

        public override bool IsSelectableBy(UnityEngine.XR.Interaction.Toolkit.Interactors.IXRSelectInteractor interactor)
        {
            return base.IsSelectableBy(interactor) && !IsSameHand(interactor);
        }

        private bool IsSameHand(UnityEngine.XR.Interaction.Toolkit.Interactors.IXRInteractor interactor)
        {
            return (interactor.IsLeftHand() ? HandType.Left : HandType.Right) == HandType;
        }
    }
}