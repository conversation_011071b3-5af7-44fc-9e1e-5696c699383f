using System;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Game.Core;
using Modules.Core;
using UnityEngine;

namespace Game.Views.PlayerUI
{
    public class RightHandMenu : HandMenu
    {
        [SerializeField] private SimpleWidget interactablesMenuWidget;
        [SerializeField] private MoneyBagWidget moneyBagWidget;

        private bool isInteractableMenuWidgetCreated, isMoneyBagWidgetCreated;

        private readonly ISubject<Unit> onInteractablesMenuClicked = new Subject<Unit>();
        private readonly ISubject<InteractableWidget> onMoneyBagMenuClicked = new Subject<InteractableWidget>();

        public IObservable<Unit> OnInteractablesMenuClicked => onInteractablesMenuClicked;
        public IObservable<InteractableWidget> OnMoneyBagMenuClicked => onMoneyBagMenuClicked;

        protected override void ShowInteractableWidgets()
        {
            interactableWidgetCancellationTokenSource = new CancellationTokenSource();

            var forward = Vector3.right;
            var up = Vector3.forward;

            var baseAngle = Config.RotationOffset;
            var angleStep = Config.AngleStep;
            for (var i = 0; i < Config.InteractableCount; i++)
            {
                var angle = baseAngle - i * angleStep;
                var rotation = Quaternion.AngleAxis(angle, up);
                var position = rotation * (Config.OrbitRadius * forward);
                CreateInteractableWidget(i, position, rotation);
            }

            baseAngle = -angleStep * 0.75f;
            for (var i = 0; i < extraSlotCount; i++)
            {
                var angle = baseAngle + i * angleStep * 1.5f;
                var rotation = Quaternion.AngleAxis(angle, up);
                var position = rotation * (Config.OrbitRadius * forward);
                CreateNewWidget(position, rotation);
            }
        }

        protected override void CreateNewWidget(Vector3 position, Quaternion rotation)
        {
            if (!isMoneyBagWidgetCreated)
            {
                TryCreateMoneyBagMenuWidget(position, rotation);
                return;
            }

            if (!isInteractableMenuWidgetCreated)
            {
                TryCreateInteractableMenuWidget(position, rotation);
            }
        }

        private bool TryCreateInteractableMenuWidget(Vector3 position, Quaternion rotation)
        {
            if (isInteractableMenuWidgetCreated)
            {
                return false;
            }

            interactablesMenuWidget.BubbleDiameter = Config.BubbleRadius;
            interactablesMenuWidget.SetOriginLocalPositionAndRotation(position, rotation);
            interactablesMenuWidget.OnClicked.Subscribe(_ => onInteractablesMenuClicked.OnNext(Unit.Default)).AddTo(interactablesMenuWidget);
            interactablesMenuWidget.OnGrabbed.Subscribe(_ => onInteractablesMenuClicked.OnNext(Unit.Default)).AddTo(interactablesMenuWidget);
            isInteractableMenuWidgetCreated = true;
            return true;
        }

        private bool TryCreateMoneyBagMenuWidget(Vector3 position, Quaternion rotation)
        {
            if (moneyBagWidget == null)
            {
                return false;
            }

            if (isMoneyBagWidgetCreated)
            {
                return false;
            }

            moneyBagWidget.SetInteractable(InventoryCodes.MoneyBag);
            moneyBagWidget.HandType = HandType.Right;
            moneyBagWidget.BubbleDiameter = Config.BubbleRadius;
            moneyBagWidget.SetOriginLocalPositionAndRotation(position, rotation);
            moneyBagWidget.OnGrabbed.Subscribe(_ => onMoneyBagMenuClicked.OnNext(moneyBagWidget)).AddTo(moneyBagWidget);
            isMoneyBagWidgetCreated = true;
            return true;
        }

        public void SetBalanceText(int balance) => moneyBagWidget.SetBalanceText(balance);

        public void SetMoneyBagActive(bool isActive) => moneyBagWidget.gameObject.SetActive(isActive);
        public void SetInteractableWidgetActive(bool isActive) => interactablesMenuWidget.gameObject.SetActive(isActive);
    }
}