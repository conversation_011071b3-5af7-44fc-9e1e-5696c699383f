using System;
using System.Reactive;
using System.Reactive.Subjects;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.SmallShop
{
    public class SimpleButton : UnityEngine.XR.Interaction.Toolkit.Interactables.XRSimpleInteractable
    {
        [<PERSON><PERSON>("But<PERSON>")]
        [SerializeField] private Transform pushNode;
        [SerializeField] private float pushOffset;

        private IAudioClient audioClient;
        private readonly ISubject<Unit> onClicked = new Subject<Unit>();

        public IObservable<Unit> OnClicked => onClicked;

        [Inject]
        private void Construct(IAudioClient audioClient)
        {
            this.audioClient = audioClient;
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            pushNode.localPosition = pushNode.localPosition.SetZ(pushOffset);
            audioClient.Play(UIAudioKeys.ButtonClick, transform.position, destroyCancellationToken);
            onClicked.OnNext(Unit.Default);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            pushNode.localPosition = pushNode.localPosition.SetZ(0);
        }
    }
}