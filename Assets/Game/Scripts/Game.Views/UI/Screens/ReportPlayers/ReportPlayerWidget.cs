using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core.Data;
using Modules.Core;
using Modules.UI;
using TMPro;
using UnityEngine;

namespace Game.Views.UI.Screens.ReportPlayers
{
    public class ReportPlayerWidget : ScrollWidget<ReportPlayerData>, IPoolItemReturnable
    {
        [SerializeField] private TMP_Text playerNameText;
        [SerializeField] private SimpleButton muteButton;
        [SerializeField] private SimpleButton reportButton;

        private CancellationTokenSource disableCancellationTokenSource;
        private CancellationTokenSource widgetCancellationTokenSource;

        protected override void OnEnable()
        {
            base.OnEnable();
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();
            muteButton.OnClicked.Subscribe(_ => HandleOption1Clicked()).AddTo(disableCancellationTokenSource.Token);
            reportButton.OnClicked.Subscribe(_ => HandleOption2Clicked()).AddTo(disableCancellationTokenSource.Token);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            disableCancellationTokenSource.CancelAndDispose();
        }

        private void OnDestroy()
        {
            widgetCancellationTokenSource.CancelAndDispose();
        }

        public void ReturnToPool()
        {
            widgetCancellationTokenSource.CancelAndDispose();
        }

        public override void SetWidgetData(ReportPlayerData widgetData)
        {
            base.SetWidgetData(widgetData);
            playerNameText.text = widgetData.playerName;

            widgetCancellationTokenSource.CancelAndDispose();
            widgetCancellationTokenSource = new CancellationTokenSource();
            widgetData.isMuted.Subscribe(muted => muteButton.SetTitle(muted ? "Unmute" : "Mute")).AddTo(widgetCancellationTokenSource.Token);
            widgetData.isReported.Subscribe(reported =>
            {
                reportButton.SetTitle(reported ? "Reported" : "Report");
                if (reported)
                {
                    muteButton.SetTitle("Unmute");
                }
            }).AddTo(widgetCancellationTokenSource.Token);
        }
    }
}