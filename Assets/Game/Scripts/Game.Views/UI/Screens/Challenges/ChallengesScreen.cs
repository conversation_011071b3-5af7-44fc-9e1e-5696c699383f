using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Views.UI.Screens.Challenges
{
    public class ChallengesScreen : GameScreen
    {
        [SerializeField] private ChallengesScreenWidget challengesScreenWidget;
        [SerializeField] private GameObject rootPanelObject;
        [SerializeField] private GameObject loadingPanelObject;

        private List<ChallengesScreenWidgetData> challengesScreenWidgetData;
        private readonly ISubject<int> onClaimChallengeButtonClicked = new Subject<int>();
        private CancellationTokenSource claimChallengeToken = new();
        private readonly List<ChallengesScreenWidget> challengesScreenWidgets = new();

        public IObservable<int> OnClaimChallengeButtonClicked => onClaimChallengeButtonClicked;

        private void Awake()
        {
            loadingPanelObject.SetActive(true);
        }

        public void SetChallengesScreenWidgetData(List<ChallengesScreenWidgetData> challengesScreenWidgetData)
        {
            this.challengesScreenWidgetData = challengesScreenWidgetData;
            loadingPanelObject.SetActive(false);
            claimChallengeToken.CancelAndDispose();
            claimChallengeToken = new CancellationTokenSource();
            RenderChallengeScreen();
        }

        private void RenderChallengeScreen()
        {
            foreach (var widget in challengesScreenWidgets)
            {
                if (widget != null) Destroy(widget.gameObject);
            }

            foreach (var widgetData in challengesScreenWidgetData)
            {
                var widget = CreateWidget(challengesScreenWidget, rootPanelObject.transform);
                LayoutRebuilder.ForceRebuildLayoutImmediate(widget.GetComponent<RectTransform>());
                widget.SetWidgetData(widgetData);
                widget.OnClaimButtonClicked.Subscribe(_ => onClaimChallengeButtonClicked.OnNext(challengesScreenWidgets.IndexOf(widget))).AddTo(claimChallengeToken.Token);
                challengesScreenWidgets.Add(widget);
            }
        }

        private void OnDestroy()
        {
            claimChallengeToken.CancelAndDispose();
        }
    }
}