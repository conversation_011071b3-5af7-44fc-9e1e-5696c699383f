using System;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI.Screens.Menu
{
    public class MenuPanel : GamePanel
    {
        [SerializeField] private ButtonWidget voxelsWidget;
        [SerializeField] private ButtonWidget moderationWidget;
        [SerializeField] private ButtonWidget cameraOpenWidget;
        [SerializeField] private ButtonWidget cameraCloseWidget;
        [SerializeField] private ButtonWidget avatarWidget;
        [SerializeField] private ButtonWidget itemsWidget;
        [SerializeField] private ButtonWidget settingsWidget;
        [SerializeField] private ButtonWidget roomSettingsWidget;
        [SerializeField] private ButtonWidget saveSceneWidget;
        [SerializeField] private ButtonWidget publishWidget;
        [SerializeField] private ButtonWidget closeWidget;
        [SerializeField] private TextWidget sceneNameWidget;
        [SerializeField] private TextWidget playerCountWidget;
        [SerializeField] private TextWidget remzBalanceWidget;
        [SerializeField] private TextWidget appVersionWidget;

        private readonly ISubject<Unit> onOpenVoxels = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenModeration = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenCamera = new Subject<Unit>();
        private readonly ISubject<Unit> onCloseCamera = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenAvatar = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenItem = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenPublish = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenSettings = new Subject<Unit>();
        private readonly ISubject<Unit> onOpenRoomSettings = new Subject<Unit>();
        private readonly ISubject<Unit> onSaveScene = new Subject<Unit>();
        private readonly ISubject<Unit> onClose = new Subject<Unit>();

        public IObservable<Unit> OnOpenVoxels => onOpenVoxels;
        public IObservable<Unit> OnOpenModeration => onOpenModeration;
        public IObservable<Unit> OnOpenCamera => onOpenCamera;
        public IObservable<Unit> OnCloseCamera => onCloseCamera;
        public IObservable<Unit> OnOpenAvatar => onOpenAvatar;
        public IObservable<Unit> OnOpenItem => onOpenItem;
        public IObservable<Unit> OnOpenPublish => onOpenPublish;
        public IObservable<Unit> OnOpenSettings => onOpenSettings;
        public IObservable<Unit> OnOpenRoomSettings => onOpenRoomSettings;
        public IObservable<Unit> OnSaveScene => onSaveScene;
        public IObservable<Unit> OnClose => onClose;

        private void Awake()
        {
            voxelsWidget.OnClicked.Subscribe(onOpenVoxels.OnNext).AddTo(destroyCancellationToken);
            moderationWidget.OnClicked.Subscribe(onOpenModeration.OnNext).AddTo(destroyCancellationToken);
            cameraOpenWidget.OnClicked.Subscribe(onOpenCamera.OnNext).AddTo(destroyCancellationToken);
            cameraCloseWidget.OnClicked.Subscribe(onCloseCamera.OnNext).AddTo(destroyCancellationToken);
            avatarWidget.OnClicked.Subscribe(onOpenAvatar.OnNext).AddTo(destroyCancellationToken);
            itemsWidget.OnClicked.Subscribe(onOpenItem.OnNext).AddTo(destroyCancellationToken);
            settingsWidget.OnClicked.Subscribe(onOpenSettings.OnNext).AddTo(destroyCancellationToken);
            roomSettingsWidget.OnClicked.Subscribe(onOpenRoomSettings.OnNext).AddTo(destroyCancellationToken);
            saveSceneWidget.OnClicked.Subscribe(onSaveScene.OnNext).AddTo(destroyCancellationToken);
            publishWidget.OnClicked.Subscribe(onOpenPublish.OnNext).AddTo(destroyCancellationToken);
            closeWidget.OnClicked.Subscribe(onClose.OnNext).AddTo(destroyCancellationToken);
        }

        public void SetSceneName(string message)
        {
            sceneNameWidget.SetMessage(message);
        }

        public void SetPlayerCount(string message)
        {
            playerCountWidget.SetMessage(message);
        }

        public void SetRemzBalance(string message)
        {
            remzBalanceWidget.SetMessage(message);
        }

        public void SetAppVersion(string message)
        {
            appVersionWidget.SetMessage(message);
        }

        public void SetActiveSaveSceneWidget(bool isActive)
        {
            saveSceneWidget.SetActive(isActive);
        }

        public void SetActivePublishSceneWidget(bool isActive)
        {
            publishWidget.SetActive(isActive);
        }

        public void SetAdminSettingsWidgetActive(bool isActive)
        {
            settingsWidget.SetActive(isActive);
        }
    }
}