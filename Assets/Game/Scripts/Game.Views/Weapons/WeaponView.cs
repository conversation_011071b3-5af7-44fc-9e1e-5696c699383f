using System.Collections.Generic;
using Game.Core.Extensions;
using Game.Views.InteractablesCore;
using UnityEngine;

namespace Game.Views.Weapons
{
    public abstract class WeaponView : InteractableView
    {
        [SerializeField] private Transform damagableCollidersNode;
        [SerializeField] private Transform notDamagableCollidersNode;

        protected readonly List<Renderer> rendererList = new();
        private readonly List<Collider> damagableColliderList = new();
        private readonly List<Collider> notDamagableColliderList = new();
        private float previewScaleMultiplier;

        private void Awake()
        {
            transform.GetComponentsInChildren(true, rendererList);
            damagableCollidersNode.GetComponentsInChildren(true, damagableColliderList);
            notDamagableCollidersNode.GetComponentsInChildren(true, notDamagableColliderList);
            AllColliderList.AddRange(damagableColliderList);
            AllColliderList.AddRange(notDamagableColliderList);
            GameUtility.IgnoreCollisions(AllColliderList);
            previewScaleMultiplier = GetPreviewScaleMultiplier();
        }

        public override void SetGrabState(bool isActiveColliders, float scale = 1)
        {
            SetScale(scale);
            SetActiveDamageableColliderList(isActiveColliders);
            SetActiveNotDamageableColliderList(false);
        }

        public override void SetPreviewState(float scale = 1)
        {
            base.SetPreviewState(scale * previewScaleMultiplier);
        }

        protected virtual float GetPreviewScaleMultiplier()
        {
            var value = GetBounds(rendererList).size.magnitude;
            return value == 0 ? 1 : 1 / value;
        }

        private void SetActiveDamageableColliderList(bool isActive)
        {
            damagableColliderList.ForEach(c => c.enabled = isActive);
        }

        private void SetActiveNotDamageableColliderList(bool isActive)
        {
            notDamagableColliderList.ForEach(c => c.enabled = isActive);
        }
    }
}