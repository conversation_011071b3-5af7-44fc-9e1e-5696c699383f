namespace Game.Views.Weapons
{
    public class SwordActor : WeaponActor<SwordView>
    {
        protected override void ChangeGrabber()
        {
            base.ChangeGrabber();
            SetActiveTrailVfx(IsGrabbed);
        }

        private void SetActiveTrailVfx(bool isActive)
        {
            if (!HasView)
            {
                return;
            }

            View.SetActiveTrailVfx(isActive);
        }
    }
}