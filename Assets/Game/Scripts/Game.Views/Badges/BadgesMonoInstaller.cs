using Modules.Core;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.Badges
{
    public class BadgesMonoInstaller : MonoInstaller
    {
        [SerializeField] private BadgesManager badgesManagerPrefab;
        [SerializeField] private BadgesConfig badgesConfig;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            builder.RegisterComponentInNewPrefab(badgesManagerPrefab, Lifetime.Singleton);
            builder.RegisterInstance(badgesConfig);
        }
    }
}