using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;

namespace Game.Models
{
    public class VoxelModel : IDisposable
    {
        private readonly ISubject<Unit> onSliceVoxelSaving = new Subject<Unit>();
        private readonly ISubject<SliceMap> onSliceMapReceived = new Subject<SliceMap>();
        private readonly ISubject<VoxelRadiusDamageArgs> onRadiusDamage = new Subject<VoxelRadiusDamageArgs>();
        private readonly ISubject<VoxelPointDamageArgs> onPointDamage = new Subject<VoxelPointDamageArgs>();

        private readonly IAsyncReactiveProperty<int> selectedVoxelId = new AsyncReactiveProperty<int>(0);
        private readonly IAsyncReactiveProperty<bool> playerHeadInWater = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<bool> playerHeadInLava = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<bool> playerBodySubmerged = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<bool> playerLeftHandSubmerged = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<bool> playerRightHandSubmerged = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<bool> playerTouchingLava = new AsyncReactiveProperty<bool>(false);

        public IReadOnlyAsyncReactiveProperty<int> SelectedVoxelId => selectedVoxelId;

        public GameObject WaterVolume { get; }
        public GameObject LavaVolume { get; }
        public List<SliceVoxel> SliceVoxels { get; } = new(10000);
        public IObservable<VoxelPointDamageArgs> OnPointDamage => onPointDamage;
        public IObservable<VoxelRadiusDamageArgs> OnRadiusDamage => onRadiusDamage;
        public IObservable<Unit> OnSliceVoxelSaving => onSliceVoxelSaving;
        public IObservable<SliceMap> OnSliceMapReceived => onSliceMapReceived;

        public IReadOnlyAsyncReactiveProperty<bool> PlayerHeadInWater => playerHeadInWater;
        public IReadOnlyAsyncReactiveProperty<bool> PlayerBodySubmerged => playerBodySubmerged;
        public IReadOnlyAsyncReactiveProperty<bool> PlayerHeadInLava => playerHeadInLava;
        public IReadOnlyAsyncReactiveProperty<bool> PlayerLeftHandSubmerged => playerLeftHandSubmerged;
        public IReadOnlyAsyncReactiveProperty<bool> PlayerRightHandSubmerged => playerRightHandSubmerged;
        public IReadOnlyAsyncReactiveProperty<bool> PlayerTouchingLava => playerTouchingLava;

        public VoxelModel(VoxelConfig voxelConfig)
        {
            if (voxelConfig.WaterVolume != null)
            {
                WaterVolume = UnityEngine.Object.Instantiate(voxelConfig.WaterVolume);
                WaterVolume?.SetActive(false);
            }

            if (voxelConfig.LavaVolume != null)
            {
                LavaVolume = UnityEngine.Object.Instantiate(voxelConfig.LavaVolume);
                LavaVolume?.SetActive(false);
            }
        }

        public void Dispose()
        {
            SliceVoxels.Clear();
        }

        public void SaveSliceVoxels()
        {
            onSliceVoxelSaving.OnNext(Unit.Default);
        }

        public void SetDamage(VoxelRadiusDamageArgs args)
        {
            onRadiusDamage.OnNext(args);
        }

        public void SetDamage(VoxelPointDamageArgs args)
        {
            onPointDamage.OnNext(args);
        }

        public void SetPlayerHeadInWater(bool inWater)
        {
            if (playerHeadInWater.Value == inWater)
            {
                return;
            }

            playerHeadInWater.Value = inWater;
        }

        public void SetPlayerBodySubmerged(bool inWater)
        {
            if (playerBodySubmerged.Value == inWater)
            {
                return;
            }

            playerBodySubmerged.Value = inWater;
        }

        public void SetPlayerHeadInLava(bool inLava)
        {
            if (playerHeadInLava.Value == inLava)
            {
                return;
            }

            playerHeadInLava.Value = inLava;
        }

        public void SetPlayerLeftHandSubmerged(bool submerged)
        {
            if (playerLeftHandSubmerged.Value == submerged)
            {
                return;
            }

            playerLeftHandSubmerged.Value = submerged;
        }

        public void SetPlayerRightHandSubmerged(bool submerged)
        {
            if (playerRightHandSubmerged.Value == submerged)
            {
                return;
            }

            playerRightHandSubmerged.Value = submerged;
        }

        public void SetPlayerTouchingLava(bool inLava)
        {
            if (playerTouchingLava.Value == inLava)
            {
                return;
            }

            playerTouchingLava.Value = inLava;
        }

        public void SetSliceMap(byte[] sliceMapBinary)
        {
            if (sliceMapBinary == null || sliceMapBinary.Length == 0)
            {
                GameLogger.Voxels.Debug("Slice map is null or empty");
                onSliceMapReceived.OnNext(null);
                return;
            }

            var sliceMap = FromBinary(sliceMapBinary);
            if (sliceMap?.voxels == null || sliceMap.voxels.Count == 0)
            {
                GameLogger.Voxels.Debug("Slice map is null or empty");
                onSliceMapReceived.OnNext(null);
                return;
            }

            GameLogger.Voxels.Debug("Slice map received. Voxel count: {0}", sliceMap.voxels.Count);
            onSliceMapReceived.OnNext(sliceMap);
        }

        public byte[] GetSliceMapBinary()
        {
            if (SliceVoxels.Count == 0)
            {
                return null;
            }

            try
            {
                var sliceMap = new SliceMap(SliceVoxels);
                return BinarySerializer.ToBytes(sliceMap);
            }
            catch (Exception e)
            {
                GameLogger.Voxels.Error(e.Message);
                return null;
            }
        }

        public void SetSelectedVoxelId(int voxelId)
        {
            selectedVoxelId.Value = voxelId;
        }

        private static SliceMap FromBinary(byte[] sliceMap)
        {
            if (sliceMap == null || sliceMap.Length == 0)
            {
                return null;
            }

            try
            {
                return BinarySerializer.FromBytes<SliceMap>(sliceMap);
            }
            catch (Exception e)
            {
                GameLogger.Voxels.Error(e.Message);
                return null;
            }
        }
    }
}