{"dependencies": {"com.cysharp.memorypack": "https://github.com/Cysharp/MemoryPack.git?path=src/MemoryPack.Unity/Assets/MemoryPack.Unity", "com.cysharp.messagepipe": "https://github.com/Cysharp/MessagePipe.git?path=src/MessagePipe.Unity/Assets/Plugins/MessagePipe", "com.cysharp.messagepipe.vcontainer": "https://github.com/Cysharp/MessagePipe.git?path=src/MessagePipe.Unity/Assets/Plugins/MessagePipe.VContainer", "com.cysharp.unitask": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "com.github-glitchenzo.nugetforunity": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "com.meta.xr.sdk.platform": "77.0.0", "com.mixpanel.unity": "https://github.com/mixpanel/mixpanel-unity.git#master", "com.unity.2d.sprite": "1.0.0", "com.unity.addressables": "2.5.0", "com.unity.ai.navigation": "2.0.8", "com.unity.burst": "1.8.23", "com.unity.collections": "2.5.7", "com.unity.ide.rider": "3.0.36", "com.unity.memoryprofiler": "1.1.6", "com.unity.mobile.android-logcat": "1.4.5", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.remote-config": "4.1.1", "com.unity.render-pipelines.universal": "17.0.4", "com.unity.services.authentication": "3.4.1", "com.unity.services.cloud-diagnostics": "1.0.10", "com.unity.services.cloudcode": "2.9.0", "com.unity.services.cloudsave": "3.2.2", "com.unity.services.economy": "3.5.1", "com.unity.services.leaderboards": "2.2.1", "com.unity.shadergraph": "17.0.4", "com.unity.test-framework": "1.5.1", "com.unity.ugui": "2.0.0", "com.unity.xr.interaction.toolkit": "3.0.8", "com.unity.xr.management": "4.5.1", "com.unity.xr.openxr": "1.14.3", "com.veriorpies.parrelsync": "https://github.com/VeriorPies/ParrelSync.git?path=/ParrelSync", "jp.hadashikick.vcontainer": "https://github.com/hadashiA/VContainer.git?path=VContainer/Assets/VContainer", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}}