{"dependencies": {"com.cysharp.memorypack": {"version": "https://github.com/Cysharp/MemoryPack.git?path=src/MemoryPack.Unity/Assets/MemoryPack.Unity", "depth": 0, "source": "git", "dependencies": {}, "hash": "fed6d9ff0e8cbae6cb14cc1d9af77e1b0e0e40da"}, "com.cysharp.messagepipe": {"version": "https://github.com/Cysharp/MessagePipe.git?path=src/MessagePipe.Unity/Assets/Plugins/MessagePipe", "depth": 0, "source": "git", "dependencies": {}, "hash": "457e787e17008ca6bf6c538bfcaa168d70ffc557"}, "com.cysharp.messagepipe.vcontainer": {"version": "https://github.com/Cysharp/MessagePipe.git?path=src/MessagePipe.Unity/Assets/Plugins/MessagePipe.VContainer", "depth": 0, "source": "git", "dependencies": {}, "hash": "cd2b1459035aa7ff59924913f09c467adf72412f"}, "com.cysharp.unitask": {"version": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "depth": 0, "source": "git", "dependencies": {}, "hash": "06067cd4c83e372310d6f79d1c625f6870ff7a2a"}, "com.github-glitchenzo.nugetforunity": {"version": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "depth": 0, "source": "git", "dependencies": {}, "hash": "f789083c31250c83082da9b29be0c976152d699b"}, "com.meta.xr.sdk.platform": {"version": "77.0.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.mixpanel.unity": {"version": "https://github.com/mixpanel/mixpanel-unity.git#master", "depth": 0, "source": "git", "dependencies": {}, "hash": "ac4a070b10c38a2b172aeaa404e5cb9fd2fe49d4"}, "com.tivadar.best.http": {"version": "file:com.tivadar.best.http", "depth": 0, "source": "embedded", "dependencies": {"com.unity.burst": "1.8.10", "com.unity.profiling.core": "1.0.0"}}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.addressables": {"version": "2.5.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.profiling.core": "1.0.2", "com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "2.4.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ai.navigation": {"version": "2.0.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.ai": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.23", "depth": 0, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.6", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.inputsystem": {"version": "1.14.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.memoryprofiler": {"version": "1.1.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.0", "com.unity.collections": "1.2.3", "com.unity.mathematics": "1.2.1", "com.unity.profiling.core": "1.0.0", "com.unity.editorcoroutines": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mobile.android-logcat": {"version": "1.4.5", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.mono-cecil": {"version": "1.11.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.remote-config": {"version": "4.1.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.0.2", "com.unity.remote-config-runtime": "4.0.2", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.remote-config-runtime": {"version": "4.0.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.services.core": "1.12.5", "com.unity.nuget.newtonsoft-json": "3.0.2", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.services.authentication": "2.7.4"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.20", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "17.0.4", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4", "com.unity.shadergraph": "17.0.4", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "2.4.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.services.authentication": {"version": "3.4.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.14.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.services.cloud-diagnostics": {"version": "1.0.10", "depth": 0, "source": "registry", "dependencies": {"com.unity.services.core": "1.12.5"}, "url": "https://packages.unity.com"}, "com.unity.services.cloudcode": {"version": "2.9.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.services.core": "1.13.0", "com.unity.services.wire": "1.2.3", "com.unity.nuget.newtonsoft-json": "3.0.2", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.services.authentication": "3.3.3"}, "url": "https://packages.unity.com"}, "com.unity.services.cloudsave": {"version": "3.2.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.services.core": "1.12.5", "com.unity.services.authentication": "3.3.1"}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.14.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.services.economy": {"version": "3.5.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.services.core": "1.13.0", "com.unity.services.authentication": "3.3.3"}, "url": "https://packages.unity.com"}, "com.unity.services.leaderboards": {"version": "2.2.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.services.core": "1.13.0", "com.unity.services.authentication": "3.3.3"}, "url": "https://packages.unity.com"}, "com.unity.services.wire": {"version": "1.3.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.services.core": "1.12.5", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.services.authentication": "2.7.4"}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.0.4", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4", "com.unity.searcher": "4.9.3"}}, "com.unity.test-framework": {"version": "1.5.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "5.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.ugui": "2.0.0"}}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.xr.core-utils": {"version": "2.5.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.xr": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.xr.interaction.toolkit": {"version": "3.0.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.xr": "1.0.0", "com.unity.inputsystem": "1.8.1", "com.unity.mathematics": "1.2.6", "com.unity.modules.audio": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.xr.core-utils": "2.2.3", "com.unity.modules.physics": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.xr.legacyinputhelpers": {"version": "2.1.12", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.vr": "1.0.0", "com.unity.modules.xr": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.xr.management": {"version": "4.5.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.vr": "1.0.0", "com.unity.modules.xr": "1.0.0", "com.unity.xr.core-utils": "2.2.1", "com.unity.modules.subsystems": "1.0.0", "com.unity.xr.legacyinputhelpers": "2.1.11"}, "url": "https://packages.unity.com"}, "com.unity.xr.openxr": {"version": "1.14.3", "depth": 0, "source": "registry", "dependencies": {"com.unity.inputsystem": "1.6.3", "com.unity.xr.core-utils": "2.3.0", "com.unity.xr.management": "4.4.0", "com.unity.xr.legacyinputhelpers": "2.1.2"}, "url": "https://packages.unity.com"}, "com.veriorpies.parrelsync": {"version": "https://github.com/VeriorPies/ParrelSync.git?path=/ParrelSync", "depth": 0, "source": "git", "dependencies": {}, "hash": "610157ad762084380380148ba8ce14e266a6da97"}, "de.lumpn.unity-discord": {"version": "file:unity-discord", "depth": 0, "source": "embedded", "dependencies": {"com.unity.ext.nunit": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "jp.hadashikick.vcontainer": {"version": "https://github.com/hadashiA/VContainer.git?path=VContainer/Assets/VContainer", "depth": 0, "source": "git", "dependencies": {}, "hash": "8d2b153809f91ca95b6d1b1579f4964473d094c1"}, "tv.liv.lck": {"version": "file:tv.liv.lck", "depth": 0, "source": "embedded", "dependencies": {"com.unity.nuget.newtonsoft-json": "2.0.2", "com.unity.textmeshpro": "2.0.0"}}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}