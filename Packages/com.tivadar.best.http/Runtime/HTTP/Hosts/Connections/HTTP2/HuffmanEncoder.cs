#if (!UNITY_WEBGL || UNITY_EDITOR) && !BESTHTTP_DISABLE_ALTERNATE_SSL

using System;

namespace Best.HTTP.Hosts.Connections.HTTP2
{
    /// <summary>
    /// A pre-generated table entry in a Huffman-Tree.
    /// </summary>
    public readonly struct HuffmanTableEntry
    {
        public readonly UInt32 Code;
        public readonly byte Bits;

        public HuffmanTableEntry(UInt32 code, byte bits)
        {
            this.Code = code;
            this.Bits = bits;
        }

        /// <summary>
        /// It must return 0 or 1 at bit index. Indexing will be relative to the Bits representing the current code. Idx grows from left to right. Idx must be between [1..Bits].
        /// </summary>
        public byte GetBitAtIdx(byte idx)
        {
            return (byte)((this.Code >> (this.Bits - idx)) & 1);
        }

        public override string ToString()
        {
            return string.Format("[TableEntry Code: 0x{0:X}, Bits: {1}]", this.Code, this.Bits);
        }
    }

    public readonly struct HuffmanTreeNode
    {
        public readonly UInt16 Value;

        public readonly UInt16 NextZeroIdx;
        public readonly UInt16 NextOneIdx;

        public HuffmanTreeNode(UInt16 value, UInt16 nextZeroIdx, UInt16 nextOneIdx)
        {
            this.Value = value;
            this.NextZeroIdx = nextZeroIdx;
            this.NextOneIdx = nextOneIdx;
        }

        public override string ToString()
        {
            return string.Format("[TreeNode Value: {0}, NextZeroIdx: {1}, NextOneIdx: {2}]",
                this.Value, this.NextZeroIdx, this.NextOneIdx);
        }
    }

    [Best.HTTP.Shared.PlatformSupport.IL2CPP.Il2CppEagerStaticClassConstruction]
    static class HuffmanEncoder
    {
        public const UInt16 EOS = 256;

        static readonly HuffmanTableEntry[] StaticTable = new HuffmanTableEntry[257]
        {
            new HuffmanTableEntry( 0x1ff8   , 13 ),
            new HuffmanTableEntry( 0x7fffd8 , 23 ),
            new HuffmanTableEntry( 0xfffffe2, 28 ),
            new HuffmanTableEntry( 0xfffffe3, 28 ),
            new HuffmanTableEntry( 0xfffffe4, 28 ),
            new HuffmanTableEntry( 0xfffffe5, 28 ),
            new HuffmanTableEntry( 0xfffffe6, 28 ),
            new HuffmanTableEntry( 0xfffffe7, 28 ),
            new HuffmanTableEntry( 0xfffffe8, 28 ),
            new HuffmanTableEntry( 0xffffea, 24 ),
            new HuffmanTableEntry( 0x3ffffffc , 30 ),
            new HuffmanTableEntry( 0xfffffe9 , 28 ),
            new HuffmanTableEntry( 0xfffffea , 28 ),
            new HuffmanTableEntry( 0x3ffffffd , 30 ),
            new HuffmanTableEntry( 0xfffffeb , 28 ),
            new HuffmanTableEntry( 0xfffffec , 28 ),
            new HuffmanTableEntry( 0xfffffed , 28 ),
            new HuffmanTableEntry( 0xfffffee , 28 ),
            new HuffmanTableEntry( 0xfffffef , 28 ),
            new HuffmanTableEntry( 0xffffff0 , 28 ),
            new HuffmanTableEntry( 0xffffff1 , 28 ),
            new HuffmanTableEntry( 0xffffff2 , 28 ),
            new HuffmanTableEntry( 0x3ffffffe, 30  ),
            new HuffmanTableEntry( 0xffffff3 , 28 ),
            new HuffmanTableEntry( 0xffffff4 , 28 ),
            new HuffmanTableEntry( 0xffffff5 , 28 ),
            new HuffmanTableEntry( 0xffffff6 , 28 ),
            new HuffmanTableEntry( 0xffffff7 , 28 ),
            new HuffmanTableEntry( 0xffffff8 , 28 ),
            new HuffmanTableEntry( 0xffffff9 , 28 ),
            new HuffmanTableEntry( 0xffffffa , 28 ),
            new HuffmanTableEntry( 0xffffffb , 28 ),
            new HuffmanTableEntry( 0x14 , 6 ),
            new HuffmanTableEntry( 0x3f8, 10  ),
            new HuffmanTableEntry( 0x3f9, 10  ),
            new HuffmanTableEntry( 0xffa, 12  ),
            new HuffmanTableEntry( 0x1ff9 , 13 ),
            new HuffmanTableEntry( 0x15 , 6 ),
            new HuffmanTableEntry( 0xf8 , 8 ),
            new HuffmanTableEntry( 0x7fa, 11  ),
            new HuffmanTableEntry( 0x3fa, 10  ),
            new HuffmanTableEntry( 0x3fb, 10  ),
            new HuffmanTableEntry( 0xf9 , 8 ),
            new HuffmanTableEntry( 0x7fb, 11  ),
            new HuffmanTableEntry( 0xfa , 8 ),
            new HuffmanTableEntry( 0x16 , 6 ),
            new HuffmanTableEntry( 0x17 , 6 ),
            new HuffmanTableEntry( 0x18 , 6 ),
            new HuffmanTableEntry( 0x0 , 5 ),
            new HuffmanTableEntry( 0x1 , 5 ),
            new HuffmanTableEntry( 0x2 , 5 ),
            new HuffmanTableEntry( 0x19 , 6 ),
            new HuffmanTableEntry( 0x1a , 6 ),
            new HuffmanTableEntry( 0x1b , 6 ),
            new HuffmanTableEntry( 0x1c , 6 ),
            new HuffmanTableEntry( 0x1d , 6 ),
            new HuffmanTableEntry( 0x1e , 6 ),
            new HuffmanTableEntry( 0x1f , 6 ),
            new HuffmanTableEntry( 0x5c , 7 ),
            new HuffmanTableEntry( 0xfb , 8 ),
            new HuffmanTableEntry( 0x7ffc , 15 ),
            new HuffmanTableEntry( 0x20 , 6 ),
            new HuffmanTableEntry( 0xffb, 12  ),
            new HuffmanTableEntry( 0x3fc, 10  ),
            new HuffmanTableEntry( 0x1ffa , 13 ),
            new HuffmanTableEntry( 0x21, 6  ),
            new HuffmanTableEntry( 0x5d, 7  ),
            new HuffmanTableEntry( 0x5e, 7  ),
            new HuffmanTableEntry( 0x5f, 7  ),
            new HuffmanTableEntry( 0x60, 7  ),
            new HuffmanTableEntry( 0x61, 7  ),
            new HuffmanTableEntry( 0x62, 7  ),
            new HuffmanTableEntry( 0x63, 7  ),
            new HuffmanTableEntry( 0x64, 7  ),
            new HuffmanTableEntry( 0x65, 7  ),
            new HuffmanTableEntry( 0x66, 7  ),
            new HuffmanTableEntry( 0x67, 7  ),
            new HuffmanTableEntry( 0x68, 7  ),
            new HuffmanTableEntry( 0x69, 7  ),
            new HuffmanTableEntry( 0x6a, 7  ),
            new HuffmanTableEntry( 0x6b, 7  ),
            new HuffmanTableEntry( 0x6c, 7  ),
            new HuffmanTableEntry( 0x6d, 7  ),
            new HuffmanTableEntry( 0x6e, 7  ),
            new HuffmanTableEntry( 0x6f, 7  ),
            new HuffmanTableEntry( 0x70, 7  ),
            new HuffmanTableEntry( 0x71, 7  ),
            new HuffmanTableEntry( 0x72, 7  ),
            new HuffmanTableEntry( 0xfc, 8  ),
            new HuffmanTableEntry( 0x73, 7  ),
            new HuffmanTableEntry( 0xfd, 8  ),
            new HuffmanTableEntry( 0x1ffb, 13  ),
            new HuffmanTableEntry( 0x7fff0, 19  ),
            new HuffmanTableEntry( 0x1ffc, 13  ),
            new HuffmanTableEntry( 0x3ffc, 14  ),
            new HuffmanTableEntry( 0x22, 6  ),
            new HuffmanTableEntry( 0x7ffd, 15  ),
            new HuffmanTableEntry( 0x3, 5  ),
            new HuffmanTableEntry( 0x23, 6  ),
            new HuffmanTableEntry( 0x4, 5  ),
            new HuffmanTableEntry( 0x24, 6  ),
            new HuffmanTableEntry( 0x5, 5  ),
            new HuffmanTableEntry( 0x25, 6  ),
            new HuffmanTableEntry( 0x26, 6  ),
            new HuffmanTableEntry( 0x27, 6  ),
            new HuffmanTableEntry( 0x6 , 5 ),
            new HuffmanTableEntry( 0x74, 7  ),
            new HuffmanTableEntry( 0x75, 7  ),
            new HuffmanTableEntry( 0x28, 6  ),
            new HuffmanTableEntry( 0x29, 6  ),
            new HuffmanTableEntry( 0x2a, 6  ),
            new HuffmanTableEntry( 0x7 , 5 ),
            new HuffmanTableEntry( 0x2b, 6  ),
            new HuffmanTableEntry( 0x76, 7  ),
            new HuffmanTableEntry( 0x2c, 6  ),
            new HuffmanTableEntry( 0x8 , 5 ),
            new HuffmanTableEntry( 0x9 , 5 ),
            new HuffmanTableEntry( 0x2d, 6  ),
            new HuffmanTableEntry( 0x77, 7  ),
            new HuffmanTableEntry( 0x78, 7  ),
            new HuffmanTableEntry( 0x79, 7  ),
            new HuffmanTableEntry( 0x7a, 7  ),
            new HuffmanTableEntry( 0x7b, 7  ),
            new HuffmanTableEntry( 0x7ffe, 15  ),
            new HuffmanTableEntry( 0x7fc, 11  ),
            new HuffmanTableEntry( 0x3ffd, 14  ),
            new HuffmanTableEntry( 0x1ffd, 13  ),
            new HuffmanTableEntry( 0xffffffc, 28  ),
            new HuffmanTableEntry( 0xfffe6 , 20 ),
            new HuffmanTableEntry( 0x3fffd2, 22  ),
            new HuffmanTableEntry( 0xfffe7 , 20 ),
            new HuffmanTableEntry( 0xfffe8 , 20 ),
            new HuffmanTableEntry( 0x3fffd3, 22  ),
            new HuffmanTableEntry( 0x3fffd4, 22  ),
            new HuffmanTableEntry( 0x3fffd5, 22  ),
            new HuffmanTableEntry( 0x7fffd9, 23  ),
            new HuffmanTableEntry( 0x3fffd6, 22  ),
            new HuffmanTableEntry( 0x7fffda, 23  ),
            new HuffmanTableEntry( 0x7fffdb, 23  ),
            new HuffmanTableEntry( 0x7fffdc, 23  ),
            new HuffmanTableEntry( 0x7fffdd, 23  ),
            new HuffmanTableEntry( 0x7fffde, 23  ),
            new HuffmanTableEntry( 0xffffeb, 24  ),
            new HuffmanTableEntry( 0x7fffdf, 23  ),
            new HuffmanTableEntry( 0xffffec, 24  ),
            new HuffmanTableEntry( 0xffffed, 24  ),
            new HuffmanTableEntry( 0x3fffd7, 22  ),
            new HuffmanTableEntry( 0x7fffe0, 23  ),
            new HuffmanTableEntry( 0xffffee, 24  ),
            new HuffmanTableEntry( 0x7fffe1, 23  ),
            new HuffmanTableEntry( 0x7fffe2, 23  ),
            new HuffmanTableEntry( 0x7fffe3, 23  ),
            new HuffmanTableEntry( 0x7fffe4, 23  ),
            new HuffmanTableEntry( 0x1fffdc, 21  ),
            new HuffmanTableEntry( 0x3fffd8, 22  ),
            new HuffmanTableEntry( 0x7fffe5, 23  ),
            new HuffmanTableEntry( 0x3fffd9, 22  ),
            new HuffmanTableEntry( 0x7fffe6, 23  ),
            new HuffmanTableEntry( 0x7fffe7, 23  ),
            new HuffmanTableEntry( 0xffffef, 24  ),
            new HuffmanTableEntry( 0x3fffda, 22  ),
            new HuffmanTableEntry( 0x1fffdd, 21  ),
            new HuffmanTableEntry( 0xfffe9 , 20 ),
            new HuffmanTableEntry( 0x3fffdb, 22  ),
            new HuffmanTableEntry( 0x3fffdc, 22  ),
            new HuffmanTableEntry( 0x7fffe8, 23  ),
            new HuffmanTableEntry( 0x7fffe9, 23  ),
            new HuffmanTableEntry( 0x1fffde, 21  ),
            new HuffmanTableEntry( 0x7fffea, 23  ),
            new HuffmanTableEntry( 0x3fffdd, 22  ),
            new HuffmanTableEntry( 0x3fffde, 22  ),
            new HuffmanTableEntry( 0xfffff0, 24  ),
            new HuffmanTableEntry( 0x1fffdf, 21  ),
            new HuffmanTableEntry( 0x3fffdf, 22  ),
            new HuffmanTableEntry( 0x7fffeb, 23  ),
            new HuffmanTableEntry( 0x7fffec, 23  ),
            new HuffmanTableEntry( 0x1fffe0, 21  ),
            new HuffmanTableEntry( 0x1fffe1, 21  ),
            new HuffmanTableEntry( 0x3fffe0, 22  ),
            new HuffmanTableEntry( 0x1fffe2, 21  ),
            new HuffmanTableEntry( 0x7fffed, 23  ),
            new HuffmanTableEntry( 0x3fffe1, 22  ),
            new HuffmanTableEntry( 0x7fffee, 23  ),
            new HuffmanTableEntry( 0x7fffef, 23  ),
            new HuffmanTableEntry( 0xfffea , 20 ),
            new HuffmanTableEntry( 0x3fffe2, 22  ),
            new HuffmanTableEntry( 0x3fffe3, 22  ),
            new HuffmanTableEntry( 0x3fffe4, 22  ),
            new HuffmanTableEntry( 0x7ffff0, 23  ),
            new HuffmanTableEntry( 0x3fffe5, 22  ),
            new HuffmanTableEntry( 0x3fffe6, 22  ),
            new HuffmanTableEntry( 0x7ffff1, 23  ),
            new HuffmanTableEntry( 0x3ffffe0, 26  ),
            new HuffmanTableEntry( 0x3ffffe1, 26  ),
            new HuffmanTableEntry( 0xfffeb , 20 ),
            new HuffmanTableEntry( 0x7fff1 , 19 ),
            new HuffmanTableEntry( 0x3fffe7, 22  ),
            new HuffmanTableEntry( 0x7ffff2, 23  ),
            new HuffmanTableEntry( 0x3fffe8, 22 ),
            new HuffmanTableEntry( 0x1ffffec, 25  ),
            new HuffmanTableEntry( 0x3ffffe2, 26  ),
            new HuffmanTableEntry( 0x3ffffe3, 26  ),
            new HuffmanTableEntry( 0x3ffffe4, 26  ),
            new HuffmanTableEntry( 0x7ffffde, 27  ),
            new HuffmanTableEntry( 0x7ffffdf, 27  ),
            new HuffmanTableEntry( 0x3ffffe5, 26  ),
            new HuffmanTableEntry( 0xfffff1 , 24 ),
            new HuffmanTableEntry( 0x1ffffed, 25  ),
            new HuffmanTableEntry( 0x7fff2 , 19 ),
            new HuffmanTableEntry( 0x1fffe3 , 21 ),
            new HuffmanTableEntry( 0x3ffffe6, 26  ),
            new HuffmanTableEntry( 0x7ffffe0, 27  ),
            new HuffmanTableEntry( 0x7ffffe1, 27  ),
            new HuffmanTableEntry( 0x3ffffe7, 26  ),
            new HuffmanTableEntry( 0x7ffffe2, 27  ),
            new HuffmanTableEntry( 0xfffff2 , 24 ),
            new HuffmanTableEntry( 0x1fffe4 , 21 ),
            new HuffmanTableEntry( 0x1fffe5 , 21 ),
            new HuffmanTableEntry( 0x3ffffe8, 26  ),
            new HuffmanTableEntry( 0x3ffffe9, 26  ),
            new HuffmanTableEntry( 0xffffffd, 28  ),
            new HuffmanTableEntry( 0x7ffffe3, 27  ),
            new HuffmanTableEntry( 0x7ffffe4, 27  ),
            new HuffmanTableEntry( 0x7ffffe5, 27  ),
            new HuffmanTableEntry( 0xfffec , 20 ),
            new HuffmanTableEntry( 0xfffff3, 24  ),
            new HuffmanTableEntry( 0xfffed , 20 ),
            new HuffmanTableEntry( 0x1fffe6, 21  ),
            new HuffmanTableEntry( 0x3fffe9, 22  ),
            new HuffmanTableEntry( 0x1fffe7, 21  ),
            new HuffmanTableEntry( 0x1fffe8, 21  ),
            new HuffmanTableEntry( 0x7ffff3, 23  ),
            new HuffmanTableEntry( 0x3fffea, 22  ),
            new HuffmanTableEntry( 0x3fffeb, 22  ),
            new HuffmanTableEntry( 0x1ffffee, 25  ),
            new HuffmanTableEntry( 0x1ffffef, 25  ),
            new HuffmanTableEntry( 0xfffff4 , 24 ),
            new HuffmanTableEntry( 0xfffff5 , 24 ),
            new HuffmanTableEntry( 0x3ffffea, 26  ),
            new HuffmanTableEntry( 0x7ffff4 , 23 ),
            new HuffmanTableEntry( 0x3ffffeb, 26  ),
            new HuffmanTableEntry( 0x7ffffe6, 27  ),
            new HuffmanTableEntry( 0x3ffffec, 26  ),
            new HuffmanTableEntry( 0x3ffffed, 26  ),
            new HuffmanTableEntry( 0x7ffffe7, 27  ),
            new HuffmanTableEntry( 0x7ffffe8, 27  ),
            new HuffmanTableEntry( 0x7ffffe9, 27  ),
            new HuffmanTableEntry( 0x7ffffea, 27  ),
            new HuffmanTableEntry( 0x7ffffeb, 27  ),
            new HuffmanTableEntry( 0xffffffe, 28  ),
            new HuffmanTableEntry( 0x7ffffec, 27  ),
            new HuffmanTableEntry( 0x7ffffed, 27  ),
            new HuffmanTableEntry( 0x7ffffee, 27  ),
            new HuffmanTableEntry( 0x7ffffef, 27  ),
            new HuffmanTableEntry( 0x7fffff0, 27  ),
            new HuffmanTableEntry( 0x3ffffee, 26  ),
            new HuffmanTableEntry( 0x3fffffff, 30 )
        };
        //static List<TreeNode> entries = new List<TreeNode>();

        static readonly HuffmanTreeNode[] HuffmanTree = new HuffmanTreeNode[]
        {
            new HuffmanTreeNode ( 0, 98, 1 ),
            new HuffmanTreeNode ( 0, 151, 2 ),
            new HuffmanTreeNode ( 0, 173, 3 ),
            new HuffmanTreeNode ( 0, 204, 4 ),
            new HuffmanTreeNode ( 0, 263, 5 ),
            new HuffmanTreeNode ( 0, 113, 6 ),
            new HuffmanTreeNode ( 0, 211, 7 ),
            new HuffmanTreeNode ( 0, 104, 8 ),
            new HuffmanTreeNode ( 0, 116, 9 ),
            new HuffmanTreeNode ( 0, 108, 10 ),
            new HuffmanTreeNode ( 0, 11, 14 ),
            new HuffmanTreeNode ( 0, 12, 166 ),
            new HuffmanTreeNode ( 0, 13, 111 ),
            new HuffmanTreeNode ( 0, 0, 0 ),
            new HuffmanTreeNode ( 0, 220, 15 ),
            new HuffmanTreeNode ( 0, 222, 16 ),
            new HuffmanTreeNode ( 0, 158, 17 ),
            new HuffmanTreeNode ( 0, 270, 18 ),
            new HuffmanTreeNode ( 0, 216, 19 ),
            new HuffmanTreeNode ( 0, 279, 20 ),
            new HuffmanTreeNode ( 0, 21, 27 ),
            new HuffmanTreeNode ( 0, 377, 22 ),
            new HuffmanTreeNode ( 0, 414, 23 ),
            new HuffmanTreeNode ( 0, 24, 301 ),
            new HuffmanTreeNode ( 0, 25, 298 ),
            new HuffmanTreeNode ( 0, 26, 295 ),
            new HuffmanTreeNode ( 1, 0, 0 ),
            new HuffmanTreeNode ( 0, 314, 28 ),
            new HuffmanTreeNode ( 0, 50, 29 ),
            new HuffmanTreeNode ( 0, 362, 30 ),
            new HuffmanTreeNode ( 0, 403, 31 ),
            new HuffmanTreeNode ( 0, 440, 32 ),
            new HuffmanTreeNode ( 0, 33, 55 ),
            new HuffmanTreeNode ( 0, 34, 46 ),
            new HuffmanTreeNode ( 0, 35, 39 ),
            new HuffmanTreeNode ( 0, 510, 36 ),
            new HuffmanTreeNode ( 0, 37, 38 ),
            new HuffmanTreeNode ( 2, 0, 0 ),
            new HuffmanTreeNode ( 3, 0, 0 ),
            new HuffmanTreeNode ( 0, 40, 43 ),
            new HuffmanTreeNode ( 0, 41, 42 ),
            new HuffmanTreeNode ( 4, 0, 0 ),
            new HuffmanTreeNode ( 5, 0, 0 ),
            new HuffmanTreeNode ( 0, 44, 45 ),
            new HuffmanTreeNode ( 6, 0, 0 ),
            new HuffmanTreeNode ( 7, 0, 0 ),
            new HuffmanTreeNode ( 0, 47, 67 ),
            new HuffmanTreeNode ( 0, 48, 63 ),
            new HuffmanTreeNode ( 0, 49, 62 ),
            new HuffmanTreeNode ( 8, 0, 0 ),
            new HuffmanTreeNode ( 0, 396, 51 ),
            new HuffmanTreeNode ( 0, 52, 309 ),
            new HuffmanTreeNode ( 0, 486, 53 ),
            new HuffmanTreeNode ( 0, 54, 307 ),
            new HuffmanTreeNode ( 9, 0, 0 ),
            new HuffmanTreeNode ( 0, 74, 56 ),
            new HuffmanTreeNode ( 0, 91, 57 ),
            new HuffmanTreeNode ( 0, 274, 58 ),
            new HuffmanTreeNode ( 0, 502, 59 ),
            new HuffmanTreeNode ( 0, 60, 81 ),
            new HuffmanTreeNode ( 0, 61, 65 ),
            new HuffmanTreeNode ( 10, 0, 0 ),
            new HuffmanTreeNode ( 11, 0, 0 ),
            new HuffmanTreeNode ( 0, 64, 66 ),
            new HuffmanTreeNode ( 12, 0, 0 ),
            new HuffmanTreeNode ( 13, 0, 0 ),
            new HuffmanTreeNode ( 14, 0, 0 ),
            new HuffmanTreeNode ( 0, 68, 71 ),
            new HuffmanTreeNode ( 0, 69, 70 ),
            new HuffmanTreeNode ( 15, 0, 0 ),
            new HuffmanTreeNode ( 16, 0, 0 ),
            new HuffmanTreeNode ( 0, 72, 73 ),
            new HuffmanTreeNode ( 17, 0, 0 ),
            new HuffmanTreeNode ( 18, 0, 0 ),
            new HuffmanTreeNode ( 0, 75, 84 ),
            new HuffmanTreeNode ( 0, 76, 79 ),
            new HuffmanTreeNode ( 0, 77, 78 ),
            new HuffmanTreeNode ( 19, 0, 0 ),
            new HuffmanTreeNode ( 20, 0, 0 ),
            new HuffmanTreeNode ( 0, 80, 83 ),
            new HuffmanTreeNode ( 21, 0, 0 ),
            new HuffmanTreeNode ( 0, 82, 512 ),
            new HuffmanTreeNode ( 22, 0, 0 ),
            new HuffmanTreeNode ( 23, 0, 0 ),
            new HuffmanTreeNode ( 0, 85, 88 ),
            new HuffmanTreeNode ( 0, 86, 87 ),
            new HuffmanTreeNode ( 24, 0, 0 ),
            new HuffmanTreeNode ( 25, 0, 0 ),
            new HuffmanTreeNode ( 0, 89, 90 ),
            new HuffmanTreeNode ( 26, 0, 0 ),
            new HuffmanTreeNode ( 27, 0, 0 ),
            new HuffmanTreeNode ( 0, 92, 95 ),
            new HuffmanTreeNode ( 0, 93, 94 ),
            new HuffmanTreeNode ( 28, 0, 0 ),
            new HuffmanTreeNode ( 29, 0, 0 ),
            new HuffmanTreeNode ( 0, 96, 97 ),
            new HuffmanTreeNode ( 30, 0, 0 ),
            new HuffmanTreeNode ( 31, 0, 0 ),
            new HuffmanTreeNode ( 0, 133, 99 ),
            new HuffmanTreeNode ( 0, 100, 129 ),
            new HuffmanTreeNode ( 0, 258, 101 ),
            new HuffmanTreeNode ( 0, 102, 126 ),
            new HuffmanTreeNode ( 0, 103, 112 ),
            new HuffmanTreeNode ( 32, 0, 0 ),
            new HuffmanTreeNode ( 0, 105, 119 ),
            new HuffmanTreeNode ( 0, 106, 107 ),
            new HuffmanTreeNode ( 33, 0, 0 ),
            new HuffmanTreeNode ( 34, 0, 0 ),
            new HuffmanTreeNode ( 0, 271, 109 ),
            new HuffmanTreeNode ( 0, 110, 164 ),
            new HuffmanTreeNode ( 35, 0, 0 ),
            new HuffmanTreeNode ( 36, 0, 0 ),
            new HuffmanTreeNode ( 37, 0, 0 ),
            new HuffmanTreeNode ( 0, 114, 124 ),
            new HuffmanTreeNode ( 0, 115, 122 ),
            new HuffmanTreeNode ( 38, 0, 0 ),
            new HuffmanTreeNode ( 0, 165, 117 ),
            new HuffmanTreeNode ( 0, 118, 123 ),
            new HuffmanTreeNode ( 39, 0, 0 ),
            new HuffmanTreeNode ( 0, 120, 121 ),
            new HuffmanTreeNode ( 40, 0, 0 ),
            new HuffmanTreeNode ( 41, 0, 0 ),
            new HuffmanTreeNode ( 42, 0, 0 ),
            new HuffmanTreeNode ( 43, 0, 0 ),
            new HuffmanTreeNode ( 0, 125, 157 ),
            new HuffmanTreeNode ( 44, 0, 0 ),
            new HuffmanTreeNode ( 0, 127, 128 ),
            new HuffmanTreeNode ( 45, 0, 0 ),
            new HuffmanTreeNode ( 46, 0, 0 ),
            new HuffmanTreeNode ( 0, 130, 144 ),
            new HuffmanTreeNode ( 0, 131, 141 ),
            new HuffmanTreeNode ( 0, 132, 140 ),
            new HuffmanTreeNode ( 47, 0, 0 ),
            new HuffmanTreeNode ( 0, 134, 229 ),
            new HuffmanTreeNode ( 0, 135, 138 ),
            new HuffmanTreeNode ( 0, 136, 137 ),
            new HuffmanTreeNode ( 48, 0, 0 ),
            new HuffmanTreeNode ( 49, 0, 0 ),
            new HuffmanTreeNode ( 0, 139, 227 ),
            new HuffmanTreeNode ( 50, 0, 0 ),
            new HuffmanTreeNode ( 51, 0, 0 ),
            new HuffmanTreeNode ( 0, 142, 143 ),
            new HuffmanTreeNode ( 52, 0, 0 ),
            new HuffmanTreeNode ( 53, 0, 0 ),
            new HuffmanTreeNode ( 0, 145, 148 ),
            new HuffmanTreeNode ( 0, 146, 147 ),
            new HuffmanTreeNode ( 54, 0, 0 ),
            new HuffmanTreeNode ( 55, 0, 0 ),
            new HuffmanTreeNode ( 0, 149, 150 ),
            new HuffmanTreeNode ( 56, 0, 0 ),
            new HuffmanTreeNode ( 57, 0, 0 ),
            new HuffmanTreeNode ( 0, 160, 152 ),
            new HuffmanTreeNode ( 0, 246, 153 ),
            new HuffmanTreeNode ( 0, 256, 154 ),
            new HuffmanTreeNode ( 0, 155, 170 ),
            new HuffmanTreeNode ( 0, 156, 169 ),
            new HuffmanTreeNode ( 58, 0, 0 ),
            new HuffmanTreeNode ( 59, 0, 0 ),
            new HuffmanTreeNode ( 0, 159, 226 ),
            new HuffmanTreeNode ( 60, 0, 0 ),
            new HuffmanTreeNode ( 0, 161, 232 ),
            new HuffmanTreeNode ( 0, 162, 224 ),
            new HuffmanTreeNode ( 0, 163, 168 ),
            new HuffmanTreeNode ( 61, 0, 0 ),
            new HuffmanTreeNode ( 62, 0, 0 ),
            new HuffmanTreeNode ( 63, 0, 0 ),
            new HuffmanTreeNode ( 0, 167, 215 ),
            new HuffmanTreeNode ( 64, 0, 0 ),
            new HuffmanTreeNode ( 65, 0, 0 ),
            new HuffmanTreeNode ( 66, 0, 0 ),
            new HuffmanTreeNode ( 0, 171, 172 ),
            new HuffmanTreeNode ( 67, 0, 0 ),
            new HuffmanTreeNode ( 68, 0, 0 ),
            new HuffmanTreeNode ( 0, 174, 189 ),
            new HuffmanTreeNode ( 0, 175, 182 ),
            new HuffmanTreeNode ( 0, 176, 179 ),
            new HuffmanTreeNode ( 0, 177, 178 ),
            new HuffmanTreeNode ( 69, 0, 0 ),
            new HuffmanTreeNode ( 70, 0, 0 ),
            new HuffmanTreeNode ( 0, 180, 181 ),
            new HuffmanTreeNode ( 71, 0, 0 ),
            new HuffmanTreeNode ( 72, 0, 0 ),
            new HuffmanTreeNode ( 0, 183, 186 ),
            new HuffmanTreeNode ( 0, 184, 185 ),
            new HuffmanTreeNode ( 73, 0, 0 ),
            new HuffmanTreeNode ( 74, 0, 0 ),
            new HuffmanTreeNode ( 0, 187, 188 ),
            new HuffmanTreeNode ( 75, 0, 0 ),
            new HuffmanTreeNode ( 76, 0, 0 ),
            new HuffmanTreeNode ( 0, 190, 197 ),
            new HuffmanTreeNode ( 0, 191, 194 ),
            new HuffmanTreeNode ( 0, 192, 193 ),
            new HuffmanTreeNode ( 77, 0, 0 ),
            new HuffmanTreeNode ( 78, 0, 0 ),
            new HuffmanTreeNode ( 0, 195, 196 ),
            new HuffmanTreeNode ( 79, 0, 0 ),
            new HuffmanTreeNode ( 80, 0, 0 ),
            new HuffmanTreeNode ( 0, 198, 201 ),
            new HuffmanTreeNode ( 0, 199, 200 ),
            new HuffmanTreeNode ( 81, 0, 0 ),
            new HuffmanTreeNode ( 82, 0, 0 ),
            new HuffmanTreeNode ( 0, 202, 203 ),
            new HuffmanTreeNode ( 83, 0, 0 ),
            new HuffmanTreeNode ( 84, 0, 0 ),
            new HuffmanTreeNode ( 0, 205, 242 ),
            new HuffmanTreeNode ( 0, 206, 209 ),
            new HuffmanTreeNode ( 0, 207, 208 ),
            new HuffmanTreeNode ( 85, 0, 0 ),
            new HuffmanTreeNode ( 86, 0, 0 ),
            new HuffmanTreeNode ( 0, 210, 213 ),
            new HuffmanTreeNode ( 87, 0, 0 ),
            new HuffmanTreeNode ( 0, 212, 214 ),
            new HuffmanTreeNode ( 88, 0, 0 ),
            new HuffmanTreeNode ( 89, 0, 0 ),
            new HuffmanTreeNode ( 90, 0, 0 ),
            new HuffmanTreeNode ( 91, 0, 0 ),
            new HuffmanTreeNode ( 0, 217, 286 ),
            new HuffmanTreeNode ( 0, 218, 276 ),
            new HuffmanTreeNode ( 0, 219, 410 ),
            new HuffmanTreeNode ( 92, 0, 0 ),
            new HuffmanTreeNode ( 0, 221, 273 ),
            new HuffmanTreeNode ( 93, 0, 0 ),
            new HuffmanTreeNode ( 0, 223, 272 ),
            new HuffmanTreeNode ( 94, 0, 0 ),
            new HuffmanTreeNode ( 0, 225, 228 ),
            new HuffmanTreeNode ( 95, 0, 0 ),
            new HuffmanTreeNode ( 96, 0, 0 ),
            new HuffmanTreeNode ( 97, 0, 0 ),
            new HuffmanTreeNode ( 98, 0, 0 ),
            new HuffmanTreeNode ( 0, 230, 240 ),
            new HuffmanTreeNode ( 0, 231, 235 ),
            new HuffmanTreeNode ( 99, 0, 0 ),
            new HuffmanTreeNode ( 0, 233, 237 ),
            new HuffmanTreeNode ( 0, 234, 236 ),
            new HuffmanTreeNode ( 100, 0, 0 ),
            new HuffmanTreeNode ( 101, 0, 0 ),
            new HuffmanTreeNode ( 102, 0, 0 ),
            new HuffmanTreeNode ( 0, 238, 239 ),
            new HuffmanTreeNode ( 103, 0, 0 ),
            new HuffmanTreeNode ( 104, 0, 0 ),
            new HuffmanTreeNode ( 0, 241, 252 ),
            new HuffmanTreeNode ( 105, 0, 0 ),
            new HuffmanTreeNode ( 0, 243, 254 ),
            new HuffmanTreeNode ( 0, 244, 245 ),
            new HuffmanTreeNode ( 106, 0, 0 ),
            new HuffmanTreeNode ( 107, 0, 0 ),
            new HuffmanTreeNode ( 0, 247, 250 ),
            new HuffmanTreeNode ( 0, 248, 249 ),
            new HuffmanTreeNode ( 108, 0, 0 ),
            new HuffmanTreeNode ( 109, 0, 0 ),
            new HuffmanTreeNode ( 0, 251, 253 ),
            new HuffmanTreeNode ( 110, 0, 0 ),
            new HuffmanTreeNode ( 111, 0, 0 ),
            new HuffmanTreeNode ( 112, 0, 0 ),
            new HuffmanTreeNode ( 0, 255, 262 ),
            new HuffmanTreeNode ( 113, 0, 0 ),
            new HuffmanTreeNode ( 0, 257, 261 ),
            new HuffmanTreeNode ( 114, 0, 0 ),
            new HuffmanTreeNode ( 0, 259, 260 ),
            new HuffmanTreeNode ( 115, 0, 0 ),
            new HuffmanTreeNode ( 116, 0, 0 ),
            new HuffmanTreeNode ( 117, 0, 0 ),
            new HuffmanTreeNode ( 118, 0, 0 ),
            new HuffmanTreeNode ( 0, 264, 267 ),
            new HuffmanTreeNode ( 0, 265, 266 ),
            new HuffmanTreeNode ( 119, 0, 0 ),
            new HuffmanTreeNode ( 120, 0, 0 ),
            new HuffmanTreeNode ( 0, 268, 269 ),
            new HuffmanTreeNode ( 121, 0, 0 ),
            new HuffmanTreeNode ( 122, 0, 0 ),
            new HuffmanTreeNode ( 123, 0, 0 ),
            new HuffmanTreeNode ( 124, 0, 0 ),
            new HuffmanTreeNode ( 125, 0, 0 ),
            new HuffmanTreeNode ( 126, 0, 0 ),
            new HuffmanTreeNode ( 0, 275, 459 ),
            new HuffmanTreeNode ( 127, 0, 0 ),
            new HuffmanTreeNode ( 0, 436, 277 ),
            new HuffmanTreeNode ( 0, 278, 285 ),
            new HuffmanTreeNode ( 128, 0, 0 ),
            new HuffmanTreeNode ( 0, 372, 280 ),
            new HuffmanTreeNode ( 0, 281, 332 ),
            new HuffmanTreeNode ( 0, 282, 291 ),
            new HuffmanTreeNode ( 0, 473, 283 ),
            new HuffmanTreeNode ( 0, 284, 290 ),
            new HuffmanTreeNode ( 129, 0, 0 ),
            new HuffmanTreeNode ( 130, 0, 0 ),
            new HuffmanTreeNode ( 0, 287, 328 ),
            new HuffmanTreeNode ( 0, 288, 388 ),
            new HuffmanTreeNode ( 0, 289, 345 ),
            new HuffmanTreeNode ( 131, 0, 0 ),
            new HuffmanTreeNode ( 132, 0, 0 ),
            new HuffmanTreeNode ( 0, 292, 296 ),
            new HuffmanTreeNode ( 0, 293, 294 ),
            new HuffmanTreeNode ( 133, 0, 0 ),
            new HuffmanTreeNode ( 134, 0, 0 ),
            new HuffmanTreeNode ( 135, 0, 0 ),
            new HuffmanTreeNode ( 0, 297, 313 ),
            new HuffmanTreeNode ( 136, 0, 0 ),
            new HuffmanTreeNode ( 0, 299, 300 ),
            new HuffmanTreeNode ( 137, 0, 0 ),
            new HuffmanTreeNode ( 138, 0, 0 ),
            new HuffmanTreeNode ( 0, 302, 305 ),
            new HuffmanTreeNode ( 0, 303, 304 ),
            new HuffmanTreeNode ( 139, 0, 0 ),
            new HuffmanTreeNode ( 140, 0, 0 ),
            new HuffmanTreeNode ( 0, 306, 308 ),
            new HuffmanTreeNode ( 141, 0, 0 ),
            new HuffmanTreeNode ( 142, 0, 0 ),
            new HuffmanTreeNode ( 143, 0, 0 ),
            new HuffmanTreeNode ( 0, 310, 319 ),
            new HuffmanTreeNode ( 0, 311, 312 ),
            new HuffmanTreeNode ( 144, 0, 0 ),
            new HuffmanTreeNode ( 145, 0, 0 ),
            new HuffmanTreeNode ( 146, 0, 0 ),
            new HuffmanTreeNode ( 0, 315, 350 ),
            new HuffmanTreeNode ( 0, 316, 325 ),
            new HuffmanTreeNode ( 0, 317, 322 ),
            new HuffmanTreeNode ( 0, 318, 321 ),
            new HuffmanTreeNode ( 147, 0, 0 ),
            new HuffmanTreeNode ( 0, 320, 341 ),
            new HuffmanTreeNode ( 148, 0, 0 ),
            new HuffmanTreeNode ( 149, 0, 0 ),
            new HuffmanTreeNode ( 0, 323, 324 ),
            new HuffmanTreeNode ( 150, 0, 0 ),
            new HuffmanTreeNode ( 151, 0, 0 ),
            new HuffmanTreeNode ( 0, 326, 338 ),
            new HuffmanTreeNode ( 0, 327, 336 ),
            new HuffmanTreeNode ( 152, 0, 0 ),
            new HuffmanTreeNode ( 0, 465, 329 ),
            new HuffmanTreeNode ( 0, 330, 355 ),
            new HuffmanTreeNode ( 0, 331, 344 ),
            new HuffmanTreeNode ( 153, 0, 0 ),
            new HuffmanTreeNode ( 0, 333, 347 ),
            new HuffmanTreeNode ( 0, 334, 342 ),
            new HuffmanTreeNode ( 0, 335, 337 ),
            new HuffmanTreeNode ( 154, 0, 0 ),
            new HuffmanTreeNode ( 155, 0, 0 ),
            new HuffmanTreeNode ( 156, 0, 0 ),
            new HuffmanTreeNode ( 0, 339, 340 ),
            new HuffmanTreeNode ( 157, 0, 0 ),
            new HuffmanTreeNode ( 158, 0, 0 ),
            new HuffmanTreeNode ( 159, 0, 0 ),
            new HuffmanTreeNode ( 0, 343, 346 ),
            new HuffmanTreeNode ( 160, 0, 0 ),
            new HuffmanTreeNode ( 161, 0, 0 ),
            new HuffmanTreeNode ( 162, 0, 0 ),
            new HuffmanTreeNode ( 163, 0, 0 ),
            new HuffmanTreeNode ( 0, 348, 360 ),
            new HuffmanTreeNode ( 0, 349, 359 ),
            new HuffmanTreeNode ( 164, 0, 0 ),
            new HuffmanTreeNode ( 0, 351, 369 ),
            new HuffmanTreeNode ( 0, 352, 357 ),
            new HuffmanTreeNode ( 0, 353, 354 ),
            new HuffmanTreeNode ( 165, 0, 0 ),
            new HuffmanTreeNode ( 166, 0, 0 ),
            new HuffmanTreeNode ( 0, 356, 366 ),
            new HuffmanTreeNode ( 167, 0, 0 ),
            new HuffmanTreeNode ( 0, 358, 368 ),
            new HuffmanTreeNode ( 168, 0, 0 ),
            new HuffmanTreeNode ( 169, 0, 0 ),
            new HuffmanTreeNode ( 0, 361, 367 ),
            new HuffmanTreeNode ( 170, 0, 0 ),
            new HuffmanTreeNode ( 0, 363, 417 ),
            new HuffmanTreeNode ( 0, 364, 449 ),
            new HuffmanTreeNode ( 0, 365, 434 ),
            new HuffmanTreeNode ( 171, 0, 0 ),
            new HuffmanTreeNode ( 172, 0, 0 ),
            new HuffmanTreeNode ( 173, 0, 0 ),
            new HuffmanTreeNode ( 174, 0, 0 ),
            new HuffmanTreeNode ( 0, 370, 385 ),
            new HuffmanTreeNode ( 0, 371, 383 ),
            new HuffmanTreeNode ( 175, 0, 0 ),
            new HuffmanTreeNode ( 0, 373, 451 ),
            new HuffmanTreeNode ( 0, 374, 381 ),
            new HuffmanTreeNode ( 0, 375, 376 ),
            new HuffmanTreeNode ( 176, 0, 0 ),
            new HuffmanTreeNode ( 177, 0, 0 ),
            new HuffmanTreeNode ( 0, 378, 393 ),
            new HuffmanTreeNode ( 0, 379, 390 ),
            new HuffmanTreeNode ( 0, 380, 384 ),
            new HuffmanTreeNode ( 178, 0, 0 ),
            new HuffmanTreeNode ( 0, 382, 437 ),
            new HuffmanTreeNode ( 179, 0, 0 ),
            new HuffmanTreeNode ( 180, 0, 0 ),
            new HuffmanTreeNode ( 181, 0, 0 ),
            new HuffmanTreeNode ( 0, 386, 387 ),
            new HuffmanTreeNode ( 182, 0, 0 ),
            new HuffmanTreeNode ( 183, 0, 0 ),
            new HuffmanTreeNode ( 0, 389, 409 ),
            new HuffmanTreeNode ( 184, 0, 0 ),
            new HuffmanTreeNode ( 0, 391, 392 ),
            new HuffmanTreeNode ( 185, 0, 0 ),
            new HuffmanTreeNode ( 186, 0, 0 ),
            new HuffmanTreeNode ( 0, 394, 400 ),
            new HuffmanTreeNode ( 0, 395, 399 ),
            new HuffmanTreeNode ( 187, 0, 0 ),
            new HuffmanTreeNode ( 0, 397, 412 ),
            new HuffmanTreeNode ( 0, 398, 402 ),
            new HuffmanTreeNode ( 188, 0, 0 ),
            new HuffmanTreeNode ( 189, 0, 0 ),
            new HuffmanTreeNode ( 0, 401, 411 ),
            new HuffmanTreeNode ( 190, 0, 0 ),
            new HuffmanTreeNode ( 191, 0, 0 ),
            new HuffmanTreeNode ( 0, 404, 427 ),
            new HuffmanTreeNode ( 0, 405, 424 ),
            new HuffmanTreeNode ( 0, 406, 421 ),
            new HuffmanTreeNode ( 0, 407, 408 ),
            new HuffmanTreeNode ( 192, 0, 0 ),
            new HuffmanTreeNode ( 193, 0, 0 ),
            new HuffmanTreeNode ( 194, 0, 0 ),
            new HuffmanTreeNode ( 195, 0, 0 ),
            new HuffmanTreeNode ( 196, 0, 0 ),
            new HuffmanTreeNode ( 0, 413, 474 ),
            new HuffmanTreeNode ( 197, 0, 0 ),
            new HuffmanTreeNode ( 0, 415, 475 ),
            new HuffmanTreeNode ( 0, 416, 471 ),
            new HuffmanTreeNode ( 198, 0, 0 ),
            new HuffmanTreeNode ( 0, 481, 418 ),
            new HuffmanTreeNode ( 0, 419, 478 ),
            new HuffmanTreeNode ( 0, 420, 435 ),
            new HuffmanTreeNode ( 199, 0, 0 ),
            new HuffmanTreeNode ( 0, 422, 423 ),
            new HuffmanTreeNode ( 200, 0, 0 ),
            new HuffmanTreeNode ( 201, 0, 0 ),
            new HuffmanTreeNode ( 0, 425, 438 ),
            new HuffmanTreeNode ( 0, 426, 433 ),
            new HuffmanTreeNode ( 202, 0, 0 ),
            new HuffmanTreeNode ( 0, 455, 428 ),
            new HuffmanTreeNode ( 0, 490, 429 ),
            new HuffmanTreeNode ( 0, 511, 430 ),
            new HuffmanTreeNode ( 0, 431, 432 ),
            new HuffmanTreeNode ( 203, 0, 0 ),
            new HuffmanTreeNode ( 204, 0, 0 ),
            new HuffmanTreeNode ( 205, 0, 0 ),
            new HuffmanTreeNode ( 206, 0, 0 ),
            new HuffmanTreeNode ( 207, 0, 0 ),
            new HuffmanTreeNode ( 208, 0, 0 ),
            new HuffmanTreeNode ( 209, 0, 0 ),
            new HuffmanTreeNode ( 0, 439, 446 ),
            new HuffmanTreeNode ( 210, 0, 0 ),
            new HuffmanTreeNode ( 0, 441, 494 ),
            new HuffmanTreeNode ( 0, 442, 461 ),
            new HuffmanTreeNode ( 0, 443, 447 ),
            new HuffmanTreeNode ( 0, 444, 445 ),
            new HuffmanTreeNode ( 211, 0, 0 ),
            new HuffmanTreeNode ( 212, 0, 0 ),
            new HuffmanTreeNode ( 213, 0, 0 ),
            new HuffmanTreeNode ( 0, 448, 460 ),
            new HuffmanTreeNode ( 214, 0, 0 ),
            new HuffmanTreeNode ( 0, 450, 467 ),
            new HuffmanTreeNode ( 215, 0, 0 ),
            new HuffmanTreeNode ( 0, 452, 469 ),
            new HuffmanTreeNode ( 0, 453, 454 ),
            new HuffmanTreeNode ( 216, 0, 0 ),
            new HuffmanTreeNode ( 217, 0, 0 ),
            new HuffmanTreeNode ( 0, 456, 484 ),
            new HuffmanTreeNode ( 0, 457, 458 ),
            new HuffmanTreeNode ( 218, 0, 0 ),
            new HuffmanTreeNode ( 219, 0, 0 ),
            new HuffmanTreeNode ( 220, 0, 0 ),
            new HuffmanTreeNode ( 221, 0, 0 ),
            new HuffmanTreeNode ( 0, 462, 488 ),
            new HuffmanTreeNode ( 0, 463, 464 ),
            new HuffmanTreeNode ( 222, 0, 0 ),
            new HuffmanTreeNode ( 223, 0, 0 ),
            new HuffmanTreeNode ( 0, 466, 468 ),
            new HuffmanTreeNode ( 224, 0, 0 ),
            new HuffmanTreeNode ( 225, 0, 0 ),
            new HuffmanTreeNode ( 226, 0, 0 ),
            new HuffmanTreeNode ( 0, 470, 472 ),
            new HuffmanTreeNode ( 227, 0, 0 ),
            new HuffmanTreeNode ( 228, 0, 0 ),
            new HuffmanTreeNode ( 229, 0, 0 ),
            new HuffmanTreeNode ( 230, 0, 0 ),
            new HuffmanTreeNode ( 231, 0, 0 ),
            new HuffmanTreeNode ( 0, 476, 477 ),
            new HuffmanTreeNode ( 232, 0, 0 ),
            new HuffmanTreeNode ( 233, 0, 0 ),
            new HuffmanTreeNode ( 0, 479, 480 ),
            new HuffmanTreeNode ( 234, 0, 0 ),
            new HuffmanTreeNode ( 235, 0, 0 ),
            new HuffmanTreeNode ( 0, 482, 483 ),
            new HuffmanTreeNode ( 236, 0, 0 ),
            new HuffmanTreeNode ( 237, 0, 0 ),
            new HuffmanTreeNode ( 0, 485, 487 ),
            new HuffmanTreeNode ( 238, 0, 0 ),
            new HuffmanTreeNode ( 239, 0, 0 ),
            new HuffmanTreeNode ( 240, 0, 0 ),
            new HuffmanTreeNode ( 0, 489, 493 ),
            new HuffmanTreeNode ( 241, 0, 0 ),
            new HuffmanTreeNode ( 0, 491, 492 ),
            new HuffmanTreeNode ( 242, 0, 0 ),
            new HuffmanTreeNode ( 243, 0, 0 ),
            new HuffmanTreeNode ( 244, 0, 0 ),
            new HuffmanTreeNode ( 0, 495, 503 ),
            new HuffmanTreeNode ( 0, 496, 499 ),
            new HuffmanTreeNode ( 0, 497, 498 ),
            new HuffmanTreeNode ( 245, 0, 0 ),
            new HuffmanTreeNode ( 246, 0, 0 ),
            new HuffmanTreeNode ( 0, 500, 501 ),
            new HuffmanTreeNode ( 247, 0, 0 ),
            new HuffmanTreeNode ( 248, 0, 0 ),
            new HuffmanTreeNode ( 249, 0, 0 ),
            new HuffmanTreeNode ( 0, 504, 507 ),
            new HuffmanTreeNode ( 0, 505, 506 ),
            new HuffmanTreeNode ( 250, 0, 0 ),
            new HuffmanTreeNode ( 251, 0, 0 ),
            new HuffmanTreeNode ( 0, 508, 509 ),
            new HuffmanTreeNode ( 252, 0, 0 ),
            new HuffmanTreeNode ( 253, 0, 0 ),
            new HuffmanTreeNode ( 254, 0, 0 ),
            new HuffmanTreeNode ( 255, 0, 0 ),
            new HuffmanTreeNode ( 256, 0, 0 )
        };

        //static HuffmanEncoder()
        //{
        //    BuildTree();
        //}
        //
        //private static void BuildTree()
        //{
        //    // Add root
        //    entries.Add(new TreeNode());
        //
        //    for (int i = 0; i < StaticTable.Length; ++i)
        //    {
        //        var tableEntry = StaticTable[i];
        //        var currentNode = entries[0];
        //        int currentNodeIdx = 0;
        //
        //        for (byte bitIdx = 1; bitIdx <= tableEntry.Bits; bitIdx++)
        //        {
        //            byte bit = tableEntry.GetBitAtIdx(bitIdx);
        //
        //            switch(bit)
        //            {
        //                case 0:
        //                    if (currentNode.NextZeroIdx == 0)
        //                    {
        //                        currentNode.NextZeroIdx = (UInt16)entries.Count;
        //                        entries[currentNodeIdx] = currentNode;
        //                        entries.Add(new TreeNode());
        //                    }
        //
        //                    currentNodeIdx = currentNode.NextZeroIdx;
        //                    currentNode = entries[currentNodeIdx];
        //                    break;
        //
        //                case 1:
        //                    if (currentNode.NextOneIdx == 0)
        //                    {
        //                        currentNode.NextOneIdx = (UInt16)entries.Count;
        //                        entries[currentNodeIdx] = currentNode;
        //                        entries.Add(new TreeNode());
        //                    }
        //
        //                    currentNodeIdx = currentNode.NextOneIdx;
        //                    currentNode = entries[currentNodeIdx];
        //                    break;
        //
        //                default:
        //                    HTTPManager.Logger.Information("HuffmanEncoder", "BuildTree - GetBitAtIdx returned with an unsupported value: " + bit);
        //                    break;
        //            }
        //        }
        //
        //        entries[currentNodeIdx] = new TreeNode { Value = (UInt16)i };
        //
        //        //HTTPManager.Logger.Information("HuffmanEncoder", string.Format("BuildTree - {0} - Entry({1}) added to idx: {2}", i, entries[currentNodeIdx], currentNodeIdx));
        //    }
        //
        //    //HTTPManager.Logger.Information("HuffmanEncoder", "BuildTree - entries: " + entries.Count);
        //    //for (int i = 0; i < entries.Count; ++i)
        //    //    HTTPManager.Logger.Information("HuffmanEncoder", string.Format("{0} - Entry : {1}", i, entries[i]));
        //    System.Text.StringBuilder sb = new System.Text.StringBuilder();
        //    for (int i = 0; i < entries.Count; ++i)
        //    {
        //        sb.AppendFormat("new TreeNode {{ Value = {0}, NextZeroIdx = {1}, NextOneIdx = {2} }},\n", entries[i].Value, entries[i].NextZeroIdx, entries[i].NextOneIdx);
        //    }
        //    UnityEngine.Debug.Log(sb.ToString());
        //}

        public static HuffmanTreeNode GetRoot()
        {
            return HuffmanTree[0];
        }

        public static HuffmanTreeNode GetNext(HuffmanTreeNode current, byte bit)
        {
            switch(bit)
            {
                case 0:
                    return HuffmanTree[current.NextZeroIdx];
                case 1:
                    return HuffmanTree[current.NextOneIdx];
            }

            throw new Exception("HuffmanEncoder - GetNext - unsupported bit: " + bit);
        }

        public static HuffmanTableEntry GetEntryForCodePoint(UInt16 codePoint)
        {
            return StaticTable[codePoint];
        }
    }
}

#endif
